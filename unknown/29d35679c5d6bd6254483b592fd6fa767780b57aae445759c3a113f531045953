<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GatewayProviderType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'auth_method',
        'http_method',
        'api_parameters_template',
        'headers_template',
        'success_response_pattern',
        'error_response_pattern',
        'supported_message_types',
        'validation_rules',
        'documentation_url',
        'is_active',
        'is_built_in'
    ];

    protected $casts = [
        'api_parameters_template' => 'array',
        'headers_template' => 'array',
        'supported_message_types' => 'array',
        'validation_rules' => 'array',
        'is_active' => 'boolean',
        'is_built_in' => 'boolean'
    ];

    /**
     * Get servers using this gateway provider type
     */
    public function servers()
    {
        return $this->hasMany(Server::class, 'gateway_type', 'name');
    }

    /**
     * Get available authentication methods
     */
    public static function getAuthMethods(): array
    {
        return [
            'api_key' => 'API Key',
            'username_password' => 'Username & Password',
            'bearer_token' => 'Bearer Token',
            'basic_auth' => 'Basic Authentication',
            'custom_header' => 'Custom Header',
            'oauth2' => 'OAuth 2.0',
            'custom' => 'Custom Authentication'
        ];
    }

    /**
     * Get available HTTP methods
     */
    public static function getHttpMethods(): array
    {
        return [
            'GET' => 'GET',
            'POST' => 'POST',
            'PUT' => 'PUT',
            'PATCH' => 'PATCH'
        ];
    }

    /**
     * Get available message types
     */
    public static function getMessageTypes(): array
    {
        return [
            'text' => 'Plain Text',
            'unicode' => 'Unicode',
            'flash' => 'Flash SMS',
            'reserved' => 'Reserved',
            'binary' => 'Binary',
            'wap_push' => 'WAP Push'
        ];
    }

    /**
     * Get parameter placeholders that can be used in API templates
     */
    public static function getParameterPlaceholders(): array
    {
        return [
            '{api_key}' => 'API Key from server configuration',
            '{username}' => 'Username from server configuration',
            '{password}' => 'Password from server configuration',
            '{bearer_token}' => 'Bearer token from server configuration',
            '{to}' => 'Recipient phone number',
            '{sender}' => 'Sender ID/Name',
            '{message}' => 'SMS message content',
            '{type}' => 'Original message type',
            '{type_mapped}' => 'Message type mapped to provider format',
            '{timestamp}' => 'Current timestamp',
            '{batch_id}' => 'Message batch identifier',
            '{company_id}' => 'Company identifier'
        ];
    }

    /**
     * Validate gateway configuration
     */
    public function validateConfiguration(array $config): array
    {
        $errors = [];
        
        if (!empty($this->validation_rules)) {
            foreach ($this->validation_rules as $field => $rules) {
                if (is_string($rules)) {
                    $rules = explode('|', $rules);
                }
                
                foreach ($rules as $rule) {
                    if ($rule === 'required' && empty($config[$field])) {
                        $errors[$field] = "The {$field} field is required.";
                    }
                    // Add more validation rules as needed
                }
            }
        }
        
        return $errors;
    }
}
