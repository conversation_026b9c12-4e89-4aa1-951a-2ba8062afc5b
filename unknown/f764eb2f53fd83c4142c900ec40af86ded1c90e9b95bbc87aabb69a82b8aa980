<?php

namespace App\Helpers\Traits;

/**
 * Common DataSet Traits
 */
trait CommonHandler
{
    /**
     * Default permissions dataSet
     *
     * @var array
     */
    protected static $status = [
        'enabled' => 'Enabled',
        'disabled' => 'Disabled',
    ];

    protected static $smsProviders = [
        'RouteMobile',
        'ElitBuzz',
        'Other',
    ];

    /**
     * Get SMS providers from database (dynamic providers)
     * Falls back to static list if database is not available
     */
    public static function smsProviders()
    {
        try {
            // Try to get providers from database
            $providers = \App\Models\GatewayProviderType::where('is_active', true)
                ->orderBy('is_built_in', 'desc')
                ->orderBy('display_name')
                ->pluck('display_name', 'name')
                ->toArray();

            if (!empty($providers)) {
                return $providers;
            }
        } catch (\Exception $e) {
            // Database might not be available during migration/seeding
        }

        // Fallback to static providers
        return array_combine(self::$smsProviders, self::$smsProviders);
    }

    protected static $paymentProviders = [
        'Ssl',
        'BkashPay',
        'Manual',
    ];

    protected static $operators = [
        'Airtel',
        'Banglalink',
        'Grameenphone',
        'Robi',
        'Teletalk',
    ];

    protected static $prefixes = [
        '88012',
        '88013',
        '88014',
        '88015',
        '88016',
        '88017',
        '88018',
        '88019',
    ];

    protected static $senderStatus = [
        'Pending Approved',
        'Approved',
        'Rejected',
    ];

    protected static $senderIdFee = 500;
}
