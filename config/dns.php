<?php

return [

    /*
    |--------------------------------------------------------------------------
    | DNS Server Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration contains the DNS server settings used for domain
    | pointing instructions and verification.
    |
    */

    'server_ip' => env('DNS_SERVER_IP', '*************'),

    /*
    |--------------------------------------------------------------------------
    | DNS Verification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for domain verification process
    |
    */

    'verification' => [
        'timeout' => env('DNS_VERIFICATION_TIMEOUT', 30),
        'cache_duration' => env('DNS_VERIFICATION_CACHE_DURATION', 300), // 5 minutes
        'max_attempts' => env('DNS_VERIFICATION_MAX_ATTEMPTS', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | DNS Record Types
    |--------------------------------------------------------------------------
    |
    | Supported DNS record types for domain verification
    |
    */

    'record_types' => [
        'A' => [
            'name' => 'A Record',
            'description' => 'Points your domain to an IP address',
            'ttl' => 3600,
        ],
        'CNAME' => [
            'name' => 'CNAME Record',
            'description' => 'Points your domain to another domain name',
            'ttl' => 3600,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Domain Verification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for domain verification process
    |
    */

    'verification' => [
        'timeout' => 30, // seconds
        'retry_attempts' => 3,
        'cache_duration' => 300, // 5 minutes in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | DNS Instructions
    |--------------------------------------------------------------------------
    |
    | Instructions for different DNS providers
    |
    */

    'instructions' => [
        'general' => [
            'title' => 'General DNS Setup',
            'steps' => [
                'Log in to your domain registrar or DNS provider',
                'Navigate to DNS management or DNS settings',
                'Add or edit an A record',
                'Set the host/name to @ (for root domain) or www (for subdomain)',
                'Set the value/points to field to: ' . env('DNS_SERVER_IP', '*************'),
                'Set TTL to 3600 (1 hour) or leave as default',
                'Save the changes',
                'Wait for DNS propagation (can take up to 24-48 hours)',
            ],
        ],
        'cloudflare' => [
            'title' => 'Cloudflare DNS Setup',
            'steps' => [
                'Log in to your Cloudflare account',
                'Select your domain',
                'Go to the DNS tab',
                'Click "Add record"',
                'Select "A" as the record type',
                'Enter "@" for Name (root domain) or "www" for subdomain',
                'Enter ' . env('DNS_SERVER_IP', '*************') . ' for IPv4 address',
                'Set Proxy status to "DNS only" (gray cloud)',
                'Click "Save"',
            ],
        ],
        'godaddy' => [
            'title' => 'GoDaddy DNS Setup',
            'steps' => [
                'Log in to your GoDaddy account',
                'Go to your product page',
                'Next to Domains, click "Manage All"',
                'Find your domain and click the DNS button',
                'In the Records section, click "Add"',
                'Select "A" from the Type dropdown',
                'Enter "@" for Host',
                'Enter ' . env('DNS_SERVER_IP', '*************') . ' for Points to',
                'Click "Save"',
            ],
        ],
        'namecheap' => [
            'title' => 'Namecheap DNS Setup',
            'steps' => [
                'Log in to your Namecheap account',
                'Go to Domain List and click "Manage" next to your domain',
                'Go to the Advanced DNS tab',
                'Click "Add New Record"',
                'Select "A Record" from the Type dropdown',
                'Enter "@" for Host',
                'Enter ' . env('DNS_SERVER_IP', '*************') . ' for Value',
                'Set TTL to "Automatic"',
                'Click the checkmark to save',
            ],
        ],
    ],

];
