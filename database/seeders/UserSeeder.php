<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //super admin
        $company1 = Company::create([
            'name' => 'SMSFrom',
            'logo' => null,
            'remarks' => 'test',
            'status' => 'enabled',
            'current_balance' => 100000,
            'balance_expired' => Carbon::now()->addYears(10),
            'margin_type' => 1,
            'minimum_recharge_amount' => 1,
        ]);

        $admin_name = 'Super Admin';

        $admin_user = User::create([
            'username' => 'superadmin',
            'password' => bcrypt('superadmin123'),
            'name' => $admin_name,
            'phone' => '01617703137',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 1,
        ]);
        $admin_user->assignRole('super-admin');
        $admin_user->companies()->attach($company1->id);

        //master reseller
        $company2 = Company::create([
            'name' => 'SMSFrom',
            'company_id' => 1,
            'logo' => null,
            'remarks' => 'test',
            'status' => 'enabled',
            'current_balance' => 100000,
            'balance_expired' => Carbon::now()->addYears(10),
            'margin_type' => 1,
            'minimum_recharge_amount' => 1,
        ]);

        $admin_name = 'Master Reseller';

        $admin_user = User::create([
            'username' => 'madmin',
            'company_id' => 1,
            'parent_id' => $admin_user->id,
            'created_by' => $admin_user->id,
            'password' => bcrypt('madmin'),
            'name' => $admin_name,
            'phone' => '01617703138',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 1,
        ]);
        $admin_user->assignRole('master-reseller');
        $admin_user->companies()->attach($company2->id);

        //reseller
        $company4 = Company::create([
            'name' => 'Reseller',
            'company_id' => 2,
            'logo' => null,
            'remarks' => 'test',
            'status' => 'enabled',
            'current_balance' => 100000,
            'balance_expired' => Carbon::now()->addYears(10),
            'margin_type' => 1,
            'minimum_recharge_amount' => 1,
        ]);

        $admin_name = 'Reseller';

        $admin_user = User::create([
            'username' => 'radmin',
            'password' => bcrypt('radmin'),
            'company_id' => 2,
            'parent_id' => $admin_user->id,
            'created_by' => $admin_user->id,
            'name' => $admin_name,
            'phone' => '01617703138',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 1,
        ]);
        $admin_user->assignRole('reseller');
        $admin_user->companies()->attach($company4->id);

        //client

        $company5 = Company::create([
            'name' => 'Client',
            'company_id' => 3,
            'logo' => null,
            'remarks' => 'test',
            'status' => 'enabled',
            'current_balance' => 100000,
            'balance_expired' => Carbon::now()->addYears(10),
            'margin_type' => 1,
            'minimum_recharge_amount' => 1,
        ]);

        $admin_name = 'Client';

        $admin_user = User::create([
            'username' => 'cadmin',
            'password' => bcrypt('cadmin'),
            'company_id' => 3,
            'parent_id' => $admin_user->id,
            'created_by' => $admin_user->id,
            'name' => $admin_name,
            'phone' => '01617703138',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 1,
        ]);
        $admin_user->assignRole('client');
        $admin_user->companies()->attach($company5->id);
    }
}
