<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define an array of permissions
        $permissions = [
            'add', 'edit', 'delete', 'update', // Generic actions
            'coverage-list', 'coverage-create', 'coverage-edit', 'coverage-delete', // Coverage permissions
            'user-list', 'user-create', 'user-edit', 'user-delete', // User permissions
            'sender-list', 'sender-create', 'sender-edit', 'sender-delete', // Sender permissions
            'gateway-list', 'gateway-create', 'gateway-edit', 'gateway-delete', // Gateway permissions
            'server-list', 'server-create', 'server-edit', 'server-delete', // Server permissions
            'role-list', 'role-create', 'role-edit', 'role-delete', // Role permissions
            'permission-list', 'permission-create', 'permission-edit', 'permission-delete', // Permission permissions
            'message-list', 'message-create', 'message-edit', 'message-delete', // Message permissions
            'reseller-create', 'settings-edit', // Additional permissions
        ];

        // Insert all permissions
        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create super-admin role and assign all permissions
        Role::create(['name' => 'super-admin'])->givePermissionTo(Permission::all());

        // Create master-reseller role and assign specific permissions
        Role::create(['name' => 'master-reseller'])->givePermissionTo([
            'reseller-create',
            'user-list', 'user-create', 'user-edit', 'user-delete',
            'coverage-list', 'coverage-edit',
            'message-list', 'message-create',
            'sender-list', 'sender-create', 'sender-delete',
        ]);

        // Create reseller role and assign specific permissions
        Role::create(['name' => 'reseller'])->givePermissionTo([
            'user-list', 'user-create', 'user-edit', 'user-delete',
            'coverage-list', 'coverage-edit',
            'message-list', 'message-create',
            'sender-list', 'sender-create', 'sender-delete',
        ]);

        // Create client role and assign specific permissions
        Role::create(['name' => 'client'])->givePermissionTo([
            'coverage-list', 'message-list', 'message-create', 'sender-list', 'sender-create', 'sender-delete',
        ]);
    }
}
