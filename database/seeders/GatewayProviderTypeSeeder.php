<?php

namespace Database\Seeders;

use App\Models\GatewayProviderType;
use Illuminate\Database\Seeder;

class GatewayProviderTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'name' => 'route_mobile',
                'display_name' => 'RouteMobile',
                'description' => 'RouteMobile SMS Gateway - HTTP-based API with parameter encoding',
                'auth_method' => 'username_password',
                'http_method' => 'GET',
                'api_parameters_template' => [
                    'username' => '{username}',
                    'password' => '{password}',
                    'source' => '{sender}',
                    'destination' => '{to}',
                    'message' => '{message}',
                    'type' => '{type_mapped}'
                ],
                'headers_template' => [],
                'success_response_pattern' => '1701',
                'error_response_pattern' => null,
                'supported_message_types' => ['text', 'flash', 'unicode', 'reserved'],
                'validation_rules' => [
                    'username' => 'required',
                    'password' => 'required',
                    'api_link' => 'required|url'
                ],
                'documentation_url' => 'https://rmlconnect.net/documentation',
                'is_active' => true,
                'is_built_in' => true
            ],
            [
                'name' => 'elit_buzz',
                'display_name' => 'ElitBuzz',
                'description' => 'ElitBuzz SMS Gateway - High-speed delivery with API key authentication',
                'auth_method' => 'api_key',
                'http_method' => 'GET',
                'api_parameters_template' => [
                    'api_key' => '{api_key}',
                    'senderid' => '{sender}',
                    'contacts' => '{to}',
                    'msg' => '{message}',
                    'type' => '{type_mapped}'
                ],
                'headers_template' => [],
                'success_response_pattern' => null,
                'error_response_pattern' => null,
                'supported_message_types' => ['text', 'unicode'],
                'validation_rules' => [
                    'api_key' => 'required',
                    'api_link' => 'required|url'
                ],
                'documentation_url' => 'https://smsfrom.net/documentation',
                'is_active' => true,
                'is_built_in' => true
            ],
            [
                'name' => 'twilio',
                'display_name' => 'Twilio',
                'description' => 'Twilio SMS API - Global SMS delivery with REST API',
                'auth_method' => 'basic_auth',
                'http_method' => 'POST',
                'api_parameters_template' => [
                    'From' => '{sender}',
                    'To' => '{to}',
                    'Body' => '{message}'
                ],
                'headers_template' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'success_response_pattern' => '"status":"queued"',
                'error_response_pattern' => '"error_code"',
                'supported_message_types' => ['text', 'unicode'],
                'validation_rules' => [
                    'username' => 'required', // Account SID
                    'password' => 'required', // Auth Token
                    'api_link' => 'required|url'
                ],
                'documentation_url' => 'https://www.twilio.com/docs/sms',
                'is_active' => true,
                'is_built_in' => true
            ],
            [
                'name' => 'nexmo',
                'display_name' => 'Vonage (Nexmo)',
                'description' => 'Vonage SMS API - Global messaging platform',
                'auth_method' => 'api_key',
                'http_method' => 'POST',
                'api_parameters_template' => [
                    'api_key' => '{api_key}',
                    'api_secret' => '{password}',
                    'from' => '{sender}',
                    'to' => '{to}',
                    'text' => '{message}',
                    'type' => '{type}'
                ],
                'headers_template' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'success_response_pattern' => '"status":"0"',
                'error_response_pattern' => '"error-text"',
                'supported_message_types' => ['text', 'unicode'],
                'validation_rules' => [
                    'api_key' => 'required',
                    'password' => 'required', // API Secret
                    'api_link' => 'required|url'
                ],
                'documentation_url' => 'https://developer.vonage.com/messaging/sms/overview',
                'is_active' => true,
                'is_built_in' => true
            ],
            [
                'name' => 'textlocal',
                'display_name' => 'Textlocal',
                'description' => 'Textlocal SMS Gateway - UK-based SMS service',
                'auth_method' => 'api_key',
                'http_method' => 'POST',
                'api_parameters_template' => [
                    'apikey' => '{api_key}',
                    'sender' => '{sender}',
                    'numbers' => '{to}',
                    'message' => '{message}'
                ],
                'headers_template' => [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'success_response_pattern' => '"status":"success"',
                'error_response_pattern' => '"status":"failure"',
                'supported_message_types' => ['text', 'unicode'],
                'validation_rules' => [
                    'api_key' => 'required',
                    'api_link' => 'required|url'
                ],
                'documentation_url' => 'https://www.textlocal.com/documentation/',
                'is_active' => true,
                'is_built_in' => true
            ],
            [
                'name' => 'custom',
                'display_name' => 'Custom Provider',
                'description' => 'Custom SMS provider with configurable parameters',
                'auth_method' => 'api_key',
                'http_method' => 'POST',
                'api_parameters_template' => [
                    'to' => '{to}',
                    'message' => '{message}',
                    'from' => '{sender}'
                ],
                'headers_template' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer {api_key}'
                ],
                'success_response_pattern' => null,
                'error_response_pattern' => null,
                'supported_message_types' => ['text', 'unicode'],
                'validation_rules' => [
                    'api_link' => 'required|url'
                ],
                'documentation_url' => null,
                'is_active' => true,
                'is_built_in' => false
            ]
        ];

        foreach ($providers as $provider) {
            GatewayProviderType::updateOrCreate(
                ['name' => $provider['name']],
                $provider
            );
        }
    }
}
