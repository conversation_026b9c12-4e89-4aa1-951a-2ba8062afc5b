<?php

namespace Database\Seeders;

use App\Models\Gateway;
use Illuminate\Database\Seeder;

class GatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Gateway::create([
            'user_id' => '1',
            'company_id' => '1',
            'name' => 'Manual',
            'class_name' => 'Manual',
            'logo' => 'media/logos/bkash.png',
            'gateway_fee' => '0.00',
            'msisdn' => '',
            'app_key' => '',
            'app_secret' => '',
            'username' => '',
            'password' => '',
            'test_mode' => 'yes',
            'status' => 'enabled',
        ]);

        Gateway::create([
            'user_id' => '1',
            'company_id' => '1',
            'name' => 'Card Payment',
            'class_name' => 'Ssl',
            'logo' => 'media/logos/ssl.png',
            'gateway_fee' => '0.02',
            'msisdn' => '',
            'username' => 'share654fcd479220f',
            'password' => 'share654fcd479220f@ssl',
            'test_mode' => 'yes',
            'status' => 'enabled',
        ]);

        Gateway::create([
            'user_id' => '1',
            'company_id' => '1',
            'name' => 'bKash',
            'class_name' => 'bKash',
            'logo' => 'media/logos/bkash.png',
            'gateway_fee' => '0.02',
            'msisdn' => '01770618575',
            'app_key' => '4f6o0cjiki2rfm34kfdadl1eqq',
            'app_secret' => '2is7hdktrekvrbljjh44ll3d9l1dtjo4pasmjvs5vl5qr3fug4b',
            'username' => 'sandboxTokenizedUser02',
            'password' => 'sandboxTokenizedUser02@12345',
            'test_mode' => 'yes',
            'status' => 'disabled',
        ]);
    }
}
