<?php

namespace Database\Seeders;

use App\Models\Coverage;
use Illuminate\Database\Seeder;

class CoverageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $coverages = [
            ['prefix' => '88013', 'operator' => 'Grameenphone'],
            ['prefix' => '88014', 'operator' => 'Banglalink'],
            ['prefix' => '88015', 'operator' => 'Teletalk'],
            ['prefix' => '88016', 'operator' => 'Airtel'],
            ['prefix' => '88017', 'operator' => 'Grameenphone'],
            ['prefix' => '88018', 'operator' => 'Robi'],
            ['prefix' => '88019', 'operator' => 'Banglalink'],
        ];

        $dataToInsert = [];

        for ($i = 1; $i <= 4; $i++) {
            foreach ($coverages as $data) {
                $dataToInsert[] = [
                    'company_id' => $i,
                    'prefix' => $data['prefix'],
                    'masking_price' => '0.50',
                    'non_masking_price' => '0.50',
                    'operator' => $data['operator'],
                    'server_id' => 1,
                    'status' => 'enabled',
                    'created_at' => now(),
                ];
            }
        }
        Coverage::insert($dataToInsert);
    }
}
