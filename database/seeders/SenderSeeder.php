<?php

namespace Database\Seeders;

use App\Models\Sender;
use Illuminate\Database\Seeder;

class SenderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        Sender::create([
            'name' => '8809617613964',
            'executed_at' => '2023-10-20 19:24:38',
            'user_id' => 1,
            'company_id' => 1,
            'is_masking' => 0,
            'is_default' => 1,
            'is_global' => 1,
            'status' => 'Approved',
        ]);

        Sender::create([
            'name' => '8809601000185',
            'executed_at' => '2023-10-20 19:24:38',
            'user_id' => 1,
            'company_id' => 1,
            'is_masking' => 0,
            'is_default' => 0,
            'is_global' => 1,
            'status' => 'Approved',
        ]);

        Sender::create([
            'name' => 'MIRAJ MOTOR',
            'executed_at' => '2023-10-20 19:24:38',
            'user_id' => 1,
            'company_id' => 1,
            'is_masking' => 1,
            'is_default' => 0,
            'status' => 'Approved',
        ]);
    }
}
