<?php

namespace Database\Seeders;

use App\Models\Server;
use Illuminate\Database\Seeder;

class ServerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Server::create([
            'name' => 'RouteMobile',
            'api_link' => 'http://apibd.rmlconnect.net/bulksms/personalizedbulksms',
            'api_key' => '',
            'username' => 'SOFTHOSTBDENT',
            'password' => 'n5d6nVsv',
            'sending_limit' => '60',
            'time_base' => '1',
            'time_unit' => 'minute',
            'enable_service' => 'all',
            'user_id' => '1',
            'status' => 'enabled',
        ]);

        Server::create([
            'name' => 'ElitBuzz',
            'api_link' => 'http://panel.smsfrom.net/smsapi',
            'api_key' => '',
            'username' => '',
            'password' => '',
            'sending_limit' => '100',
            'time_base' => '1',
            'time_unit' => 'minute',
            'enable_service' => 'all',
            'user_id' => '1',
            'status' => 'enabled',
        ]);
    }
}
