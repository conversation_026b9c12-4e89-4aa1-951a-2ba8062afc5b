<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::unprepared('CREATE TRIGGER create_new_transaction
            AFTER UPDATE ON companies
            FOR EACH ROW
            BEGIN
                DECLARE amount_in DECIMAL(10,2);
                DECLARE amount_out DECIMAL(10,2);

                IF NEW.current_balance != OLD.current_balance THEN
                    SET amount_in = IF(NEW.current_balance > OLD.current_balance, NEW.current_balance - OLD.current_balance, 0);
                    SET amount_out = IF(NEW.current_balance < OLD.current_balance, OLD.current_balance - NEW.current_balance, 0);

                    INSERT INTO transactions
                    (user_id, amount_in, amount_out, date, company_id, remarks, created_at, updated_at)
                    VALUES (
                        1,
                        amount_in,
                        amount_out,
                        CURRENT_DATE,
                        NEW.id,
                        NEW.remarks,
                        NOW(),
                        NOW()
                    );
                END IF;
            END');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP TRIGGER create_new_transaction');
    }
};
