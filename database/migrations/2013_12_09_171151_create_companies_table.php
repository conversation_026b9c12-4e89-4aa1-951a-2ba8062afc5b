<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('company_id')->nullable();
            $table->decimal('current_balance', 10, 2)->default(0.00);
            $table->timestamp('balance_expired')->nullable();
            $table->string('margin_type')->nullable();
            $table->decimal('minimum_recharge_amount', 10, 2);
            $table->string('name')->nullable();
            $table->string('logo')->nullable();
            $table->string('remarks')->nullable();
            $table->string('status')->default('enabled');
            $table->json('data')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
