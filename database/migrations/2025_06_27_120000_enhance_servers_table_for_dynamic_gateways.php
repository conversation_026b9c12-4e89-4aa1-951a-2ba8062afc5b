<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('servers', function (Blueprint $table) {
            // Add new columns for dynamic gateway configuration
            $table->string('gateway_type')->default('custom')->after('name'); // e.g., 'route_mobile', 'elit_buzz', 'custom'
            $table->json('gateway_config')->nullable()->after('gateway_type'); // Dynamic configuration
            $table->string('auth_method')->default('api_key')->after('gateway_config'); // 'api_key', 'username_password', 'bearer_token', 'custom'
            $table->string('http_method')->default('GET')->after('auth_method'); // 'GET', 'POST', 'PUT'
            $table->json('api_parameters')->nullable()->after('http_method'); // Dynamic API parameter mapping
            $table->json('headers')->nullable()->after('api_parameters'); // Custom headers
            $table->text('success_response_pattern')->nullable()->after('headers'); // Pattern to identify successful responses
            $table->text('error_response_pattern')->nullable()->after('success_response_pattern'); // Pattern to identify errors
            $table->json('supported_message_types')->nullable()->after('error_response_pattern'); // ['text', 'unicode', 'flash', etc.]
            $table->boolean('is_legacy')->default(false)->after('supported_message_types'); // Mark legacy providers
        });

        // Update existing records to maintain backward compatibility
        DB::table('servers')->where('name', 'RouteMobile')->update([
            'gateway_type' => 'route_mobile',
            'auth_method' => 'username_password',
            'http_method' => 'GET',
            'api_parameters' => json_encode([
                'username' => '{username}',
                'password' => '{password}',
                'source' => '{sender}',
                'destination' => '{to}',
                'message' => '{message}',
                'type' => '{type_mapped}'
            ]),
            'success_response_pattern' => '1701',
            'supported_message_types' => json_encode(['text', 'flash', 'unicode', 'reserved']),
            'is_legacy' => true
        ]);

        DB::table('servers')->where('name', 'ElitBuzz')->update([
            'gateway_type' => 'elit_buzz',
            'auth_method' => 'api_key',
            'http_method' => 'GET',
            'api_parameters' => json_encode([
                'api_key' => '{api_key}',
                'senderid' => '{sender}',
                'contacts' => '{to}',
                'msg' => '{message}',
                'type' => '{type_mapped}'
            ]),
            'supported_message_types' => json_encode(['text', 'unicode']),
            'is_legacy' => true
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('servers', function (Blueprint $table) {
            $table->dropColumn([
                'gateway_type',
                'gateway_config',
                'auth_method',
                'http_method',
                'api_parameters',
                'headers',
                'success_response_pattern',
                'error_response_pattern',
                'supported_message_types',
                'is_legacy'
            ]);
        });
    }
};
