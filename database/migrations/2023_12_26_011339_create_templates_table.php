<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable()->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->string('name');
            $table->text('text')->nullable();
            $table->tinyInteger('is_pattern')->default(1);
            $table->string('status')->nullable()->default('enabled');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('templates');
    }
};
