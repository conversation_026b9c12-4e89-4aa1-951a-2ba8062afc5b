<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('amount_in', 10, 2)->nullable()->default(0);
            $table->decimal('amount_out', 10, 2)->nullable()->default(0);
            $table->timestamp('date')->nullable();
            $table->string('remarks')->nullable();
            $table->integer('company_id')->unsigned()->index();
            $table->text('log')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
