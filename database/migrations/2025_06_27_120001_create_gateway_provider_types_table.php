<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gateway_provider_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // e.g., 'route_mobile', 'elit_buzz', 'twilio'
            $table->string('display_name'); // e.g., 'RouteMobile', 'ElitBuzz', 'Twilio'
            $table->text('description')->nullable();
            $table->string('auth_method'); // 'api_key', 'username_password', etc.
            $table->string('http_method')->default('GET'); // 'GET', 'POST', etc.
            $table->json('api_parameters_template'); // Template for API parameters
            $table->json('headers_template')->nullable(); // Template for headers
            $table->text('success_response_pattern')->nullable(); // Pattern to identify success
            $table->text('error_response_pattern')->nullable(); // Pattern to identify errors
            $table->json('supported_message_types'); // ['text', 'unicode', etc.]
            $table->json('validation_rules')->nullable(); // Validation rules for configuration
            $table->string('documentation_url')->nullable(); // Link to provider documentation
            $table->boolean('is_active')->default(true);
            $table->boolean('is_built_in')->default(false); // Built-in vs custom providers
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gateway_provider_types');
    }
};
