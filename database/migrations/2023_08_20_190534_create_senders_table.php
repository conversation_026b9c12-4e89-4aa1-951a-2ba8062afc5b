<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('senders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->index();
            $table->foreignId('company_id')->nullable()->unsigned()->index(); // Add unique name
            $table->foreignId('server_id')->nullable()->unsigned()->index();  // Add unique name
            $table->string('name')->unique(); // Sender name (from number)
            $table->timestamp('executed_at')->nullable();
            $table->boolean('is_default')->default(false); // Indicates if this is the default sender globally
            $table->boolean('is_server_default')->default(false); // Indicates if this is the default for the server
            $table->boolean('is_global')->default(false); // Global sender available for all users
            $table->boolean('is_masking')->default(false); // Indicates masking or non-masking sender type
            $table->json('required_documents')->nullable(); // JSON field for storing multiple documents
            $table->string('status')->default('enabled');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('senders');
    }
};
