<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->enum('dns_status', ['pending', 'active', 'failed'])->default('pending')->after('domain');
            $table->timestamp('last_verified_at')->nullable()->after('dns_status');
            $table->text('verification_notes')->nullable()->after('last_verified_at');
            $table->integer('verification_attempts')->default(0)->after('verification_notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropColumn(['dns_status', 'last_verified_at', 'verification_notes', 'verification_attempts']);
        });
    }
};
