<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('servers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('api_link');
            $table->string('api_key');
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->integer('sending_limit')->nullable();
            $table->integer('time_base')->nullable();
            $table->string('time_unit')->nullable();
            $table->string('enable_service')->nullable();
            $table->integer('user_id')->unsigned()->index();
            $table->string('status')->default('enabled');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('servers');
    }
};
