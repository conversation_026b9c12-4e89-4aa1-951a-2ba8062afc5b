<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable()->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('sender_id')->unsigned()->index();
            $table->integer('server_id')->unsigned()->index();
            $table->string('phone_number');
            $table->string('operator')->nullable();
            $table->string('batch_number')->index();
            $table->string('sms_type');
            $table->integer('sms_count');
            $table->float('sms_cost')->nullable();
            $table->text('sms_content');
            $table->timestamp('schedule_at')->nullable();
            $table->json('api_response')->nullable();
            $table->string('status')->nullable()->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
