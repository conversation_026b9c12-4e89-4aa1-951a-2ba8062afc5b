<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coverages', function (Blueprint $table) {
            $table->id();
            $table->integer('company_id')->nullable()->unsigned()->index();
            $table->integer('server_id')->nullable()->unsigned()->index();
            $table->string('prefix');
            $table->float('masking_price')->default(0);
            $table->float('non_masking_price')->default(0);
            $table->string('operator')->nullable();
            $table->string('status')->default('enabled');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coverages');
    }
};
