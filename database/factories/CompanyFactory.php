<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company,
            'company_id' => $this->faker->unique()->numerify('COMP####'),
            'logo' => null,
            'remarks' => $this->faker->optional()->sentence,
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'current_balance' => $this->faker->randomFloat(2, 0, 10000),
            'balance_expired' => $this->faker->optional()->dateTimeBetween('now', '+1 year'),
            'api_key' => $this->faker->unique()->uuid,
            'margin_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'minimum_recharge_amount' => $this->faker->randomFloat(2, 10, 100),
        ];
    }

    /**
     * Indicate that the company is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the company is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the company has a high balance.
     */
    public function withHighBalance(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_balance' => $this->faker->randomFloat(2, 5000, 50000),
        ]);
    }

    /**
     * Indicate that the company has a low balance.
     */
    public function withLowBalance(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_balance' => $this->faker->randomFloat(2, 0, 100),
        ]);
    }
}
