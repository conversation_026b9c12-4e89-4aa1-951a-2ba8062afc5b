<?php

namespace Database\Factories;

use App\Models\Server;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Server>
 */
class ServerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Server::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company . ' SMS Server',
            'api_link' => $this->faker->url,
            'gateway_type' => $this->faker->randomElement(['route_mobile', 'elit_buzz', 'twilio', 'nexmo']),
            'auth_method' => $this->faker->randomElement(['api_key', 'username_password', 'basic_auth']),
            'http_method' => $this->faker->randomElement(['GET', 'POST']),
            'api_key' => $this->faker->uuid,
            'username' => $this->faker->userName,
            'password' => $this->faker->password,
            'api_parameters' => [
                'to' => '{to}',
                'message' => '{message}',
                'from' => '{sender}'
            ],
            'headers' => [
                'Content-Type' => 'application/json'
            ],
            'success_response_pattern' => 'success',
            'error_response_pattern' => 'error',
            'supported_message_types' => ['text', 'unicode'],
            'is_legacy' => false,
            'sending_limit' => $this->faker->numberBetween(100, 1000),
            'time_base' => $this->faker->numberBetween(1, 24),
            'time_unit' => $this->faker->randomElement(['hour', 'day']),
            'enable_service' => true,
            'status' => $this->faker->randomElement(['enabled', 'disabled']),
            'user_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the server is a legacy provider.
     */
    public function legacy(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_legacy' => true,
            'gateway_type' => $this->faker->randomElement(['route_mobile', 'elit_buzz']),
        ]);
    }

    /**
     * Indicate that the server uses API key authentication.
     */
    public function apiKey(): static
    {
        return $this->state(fn (array $attributes) => [
            'auth_method' => 'api_key',
            'api_key' => $this->faker->uuid,
            'username' => null,
            'password' => null,
        ]);
    }

    /**
     * Indicate that the server uses username/password authentication.
     */
    public function usernamePassword(): static
    {
        return $this->state(fn (array $attributes) => [
            'auth_method' => 'username_password',
            'username' => $this->faker->userName,
            'password' => $this->faker->password,
            'api_key' => null,
        ]);
    }

    /**
     * Indicate that the server uses basic auth.
     */
    public function basicAuth(): static
    {
        return $this->state(fn (array $attributes) => [
            'auth_method' => 'basic_auth',
            'username' => $this->faker->userName,
            'password' => $this->faker->password,
            'api_key' => null,
        ]);
    }

    /**
     * Indicate that the server is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'enabled',
            'enable_service' => true,
        ]);
    }

    /**
     * Indicate that the server is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'disabled',
            'enable_service' => false,
        ]);
    }

    /**
     * Indicate that the server uses custom gateway configuration.
     */
    public function custom(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway_type' => 'custom',
            'auth_method' => 'api_key',
            'http_method' => 'POST',
            'is_legacy' => false,
        ]);
    }

    /**
     * Create a Twilio server configuration.
     */
    public function twilio(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Twilio SMS Server',
            'api_link' => 'https://api.twilio.com/2010-04-01/Accounts/test/Messages.json',
            'gateway_type' => 'twilio',
            'auth_method' => 'basic_auth',
            'http_method' => 'POST',
            'api_parameters' => [
                'From' => '{sender}',
                'To' => '{to}',
                'Body' => '{message}'
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'success_response_pattern' => '"status":"queued"',
            'error_response_pattern' => '"error_code"',
            'supported_message_types' => ['text', 'unicode'],
            'is_legacy' => false,
        ]);
    }

    /**
     * Create a RouteMobile server configuration.
     */
    public function routeMobile(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'RouteMobile',
            'api_link' => 'https://rmlconnect.net/bulksms/bulksms',
            'gateway_type' => 'route_mobile',
            'auth_method' => 'username_password',
            'http_method' => 'GET',
            'api_parameters' => [
                'username' => '{username}',
                'password' => '{password}',
                'source' => '{sender}',
                'destination' => '{to}',
                'message' => '{message}',
                'type' => '{type_mapped}'
            ],
            'headers' => [],
            'success_response_pattern' => '1701',
            'error_response_pattern' => null,
            'supported_message_types' => ['text', 'flash', 'unicode', 'reserved'],
            'is_legacy' => true,
        ]);
    }
}
