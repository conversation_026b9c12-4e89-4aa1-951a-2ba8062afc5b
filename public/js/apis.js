"use strict";

var token = $("meta[name=csrf-token]").attr("content");

function Request(url, method, data = {}, success = () => {}, error = () => {}, headers = {}) {
    if (method.toUpperCase() === 'POST') {
        data = {
            ...data,
            _token: token
        }
    }

    $.ajax({
        url: url,
        type: method,
        data: data,
        headers: {
            'X-CSRF-TOKEN': token,
            ...headers
        },
        dataType: 'json',
        success: success,
        error: error,
    });
}

// Define a global function for the delete action
$(document).on('click', '.delete-button', function () {
    let endPoint = $(this).attr('data-endpoint');
    Swal.fire({
        text: "Are you sure you would like to delete?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`${endPoint}`,
                'DELETE',
                {},
                (resp) => {
                    if (!window.dataTable) {
                        window.location.href = window.location.href;
                    }
                    window.dataTable.draw();
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });
});

$(document).on('click', '.delete-button-view', function () {
    let endPoint = $(this).attr('href'); // Use the 'href' attribute for the endpoint
    let redirectUrl = $(this).data('redirect-url'); // Use 'data-redirect-url' attribute for redirection
    Swal.fire({
        text: "Are you sure you would like to delete?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`${endPoint}`,
                'DELETE',
                {},
                (resp) => {
                    if (resp.success) {
                        window.location.href = redirectUrl; // Redirect to specified route
                    }
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });

    return false; // Prevent default link behavior
});


// Define a global function for the close action
$(document).on('click', '.cancel-button', function (e) {
    e.preventDefault();
    let modelId = $(this).attr('data-model');
    const element = document.getElementById(modelId);
    const form = element.querySelector(`#${modelId}_form`);
    const modal = $(`#${modelId}`)

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            form.reset();
            modal.modal('hide');
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});


$(document).on('click', '.edit-user', function (e) {
    e.preventDefault();
    let endPoint = $(this).attr('data-endpoint');
    Request(`${endPoint}`,
        'GET',
        {},
        (data) => {
        let user = data.data;

        $('#roleId').val(user.role_id);
        $('#name').val(user.name);
        $('#phone').val(user.phone);
        $('#email').val(user.email);


        }, (e) => {
            Swal.fire({
                text: e.responseJSON.message,
                icon: "warning"
            });
        }
    );
});


function showToast(message, icon, timer = 0) {
    Swal.fire({
        text: message,
        icon: icon,
        buttonsStyling: false,
        timer: timer,
        showConfirmButton: timer === 0,
        confirmButtonText: "Ok, got it!",
        customClass: {
            confirmButton: "btn btn-primary"
        }
    });
}
