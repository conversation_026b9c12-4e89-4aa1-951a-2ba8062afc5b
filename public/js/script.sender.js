"use strict";

const element = document.getElementById('kt_modal_add_sender');
const form = element.querySelector('#kt_modal_add_sender_form');
const modal = new bootstrap.Modal(element);

$("#add-sender").on('click', function (e) {
    e.preventDefault();

    let senderName = $("#sender-name").val();
    if (senderName.trim() === "") {
        Swal.fire({
            text: "Sender name must not be empty.",
            icon: "warning",
            showCancelButton: true,
            cancelButtonText: "Cancel"
        });
        return;
    }

    // Create FormData object to handle form submission with files
    let formData = new FormData(form);

    $.ajax({
        url: '/web-api/add-sender',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
            $("#sender-name").val("");
            modal.hide();
            Swal.fire({
                text: response.message || "Sender added successfully!",
                icon: "success"
            });


            $("#kt_modal_add_sender_form").trigger("reset"); // Clear the form
            $("#file-preview").html(''); // Clear the file preview
            $('#kt_modal_add_sender').modal('hide'); // Hide modal

            if (window.dataTable) {
                window.dataTable.draw(); // Redraw DataTable if initialized
            } else {
                window.location.reload();
            }
        },
        error: function (e) {
            Swal.fire({
                text: e.responseJSON?.message || "An error occurred. Please try again.",
                icon: "warning"
            });
        }
    });
});


// Cancel button handler
const cancelButton = element.querySelector('[data-kt-modal-action="cancel"]');
cancelButton.addEventListener('click', e => {
    e.preventDefault();

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            $("#kt_modal_add_sender_form").trigger("reset"); // Clear the form
            $("#file-preview").html(''); // Clear the file preview
            form.reset(); // Reset form
            modal.hide();
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});

// Close button handler
const closeButton = element.querySelector('[data-kt-modal-action="close"]');
closeButton.addEventListener('click', e => {
    e.preventDefault();

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            form.reset(); // Reset form
            modal.hide();
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});


// Define a global function for the delete action
$(document).on('click', '.make-default', function () {
    let endPoint = $(this).attr('data-endpoint');
    Swal.fire({
        text: "Are you sure you would like to make default?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, make default!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`${endPoint}`,
                'POST',
                {},
                (resp) => {
                    if (!window.dataTable) {
                        window.location.href = window.location.href;
                    }
                    window.dataTable.draw();
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });
});
