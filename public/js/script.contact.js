"use strict";

$("#add-group").on('click', function(){
    let groupName = $("#group-name").val();

    if (! groupName) {
        Swal.fire({
            text: "Group name must not be empty.",
            icon: "warning",
            showCancelButton: true,
            cancelButtonText: "Cancel",
        });

        return;
    }

    Request('/add-group',
        'POST',
        {
            name: groupName
        },
        () => {
            window.location.href = window.location.href;
        },(e) => {
            Swal.fire({
                text: e.responseJSON.message,
                icon: "warning"
            });
        }
    );
})

$(".delete-info").on('click', function(){
    let endPoint = $(this).attr('data-endpoint');
    let id = $(this).attr('data-id');

    Swal.fire({
        text: "Are you sure you would like to delete?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.isConfirmed) {
            Request(`/web-api/delete-${endPoint}/${id}`,
                'DELETE',
                {},
                () => {
                    window.location.href = window.location.href;
                }, (e) => {
                    Swal.fire({
                        text: e.responseJSON.message,
                        icon: "warning"
                    });
                }
            );
        }
    });

});
