"use strict";

const element = document.getElementById('kt_modal_add_balance');
const form = element.querySelector('#kt_modal_add_balance_form');
const modal = new bootstrap.Modal(element);

$('#kt_modal_add_balance').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget);
    var userId = button.data('user');
    $('#user_id').val(userId);
});

$("#add-balance").on('click', function (e) {
    e.preventDefault(); // Prevent default form submission

   /* let action = $("input[name='action']:checked").val();*/
    let amount = $("#amount").val();
    let user_id = $("#user_id").val();
    let gateway_fee = $("#gateway_fee").val();
    let payment_method = $("#payment_method").val();
   /* let validity = $("select[name='validity']").val();*/
    let remarks = $("textarea[name='remarks']").val();

    if (!user_id || !payment_method || amount.trim() === "" || remarks.trim() === "") {
        Swal.fire({
            text: "All fields are required.",
            icon: "warning",
            showCancelButton: true,
            cancelButtonText: "Cancel"
        });
        return;
    }

    Request('/web-api/add-balance',
        'POST',
        {
         /*   action: action,*/
            recharge_amount: amount,
            user_id: user_id,
            remarks: remarks,
            gateway_fee: gateway_fee,
            payment_method: payment_method,
        },
        () => {
            form.reset();
            modal.hide();
            Swal.fire({
                text: "Balance updated successfully!",
                icon: "success"
            });
            if (!window.dataTable) {
                window.location.href = window.location.href;
            }
            window.dataTable.draw();
        }, (e) => {
            console.log(e)
            modal.hide();
            Swal.fire({
                text: "Sonething went wrong",
                icon: "warning"
            });
        }
    );
});

// Cancel button handler
const cancelButton = element.querySelector('[data-kt-modal-action="cancel"]');
cancelButton.addEventListener('click', e => {
    e.preventDefault();

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            form.reset(); // Reset form
            modal.hide();
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});

// Close button handler
const closeButton = element.querySelector('[data-kt-modal-action="close"]');
closeButton.addEventListener('click', e => {
    e.preventDefault();

    Swal.fire({
        text: "Are you sure you would like to cancel?",
        icon: "warning",
        showCancelButton: true,
        buttonsStyling: false,
        confirmButtonText: "Yes, cancel it!",
        cancelButtonText: "No, return",
        customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-active-light"
        }
    }).then(function (result) {
        if (result.value) {
            form.reset(); // Reset form
            modal.hide();
        } else if (result.dismiss === 'cancel') {
            Swal.fire({
                text: "Your form has not been cancelled!.",
                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Ok, got it!",
                customClass: {
                    confirmButton: "btn btn-primary",
                }
            });
        }
    });
});
