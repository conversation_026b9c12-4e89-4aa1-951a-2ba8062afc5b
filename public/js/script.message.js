"use strict";

const element = document.getElementById('kt_modal_show_template');
const modal = new bootstrap.Modal(element);

// Close button handler
const closeButton = element.querySelector('[data-kt-modal-action="close"]');
closeButton.addEventListener('click', e => {
    e.preventDefault();
    modal.hide();
});

function containsDoubleByteCharacters(text) {
    const doubleByteRegex = /[^\x00-\x7F]/;
    return doubleByteRegex.test(text);
}

const smsContentInput = document.getElementById('smsContent');
const smsCountInfo = document.getElementById('smsCountInfo');
const maximumCharactersEnglish = 1530;
const maximumCharactersUnicode = 670;

smsContentInput.addEventListener('input', updateCharacterCount);

function updateCharacterCount() {
    const smsContent = smsContentInput.value;
    const currentLength = smsContent.length;

    const isEnglishContent = !containsDoubleByteCharacters(smsContent);
    const maximumCharacters = isEnglishContent ? maximumCharactersEnglish : maximumCharactersUnicode;

    const remainingCharacters = maximumCharacters - currentLength;
    const smsCount = Math.ceil(currentLength / (isEnglishContent ? 160 : 70));

    smsCountInfo.textContent = `${currentLength} Characters | ${remainingCharacters} Characters Left | ${smsCount} SMS (${isEnglishContent ? 160 : 70} Char./SMS)`;
}

$(document).ready(function () {

    function toggleDatePicker() {
        const scheduledAtDiv = $('.scheduled_at');
        if ($('input[name="is_scheduled"][value="1"]').prop('checked')) {
            scheduledAtDiv.show();
        } else {
            scheduledAtDiv.hide();
        }
    }

    $('input[name="is_scheduled"]').change(toggleDatePicker);
    toggleDatePicker();

    function resizeTextarea() {
        const textarea = document.getElementById('smsContent');
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    }

    $('#smsContent').on('input', resizeTextarea);

    $(document).on('click', '.insert-button', function () {
        const text = $(this).attr('data-text');
        $('#smsContent').val(text);
        resizeTextarea();
        modal.hide();
    });

    function showToast(message, type = 'error', duration = 3000) {
        toastr[type](message, type.toUpperCase(), {timeOut: duration});
    }

    $('#sms_recipient').submit(function (e) {
        const senderId = $('[name="sender_id"]').val();
        const phoneNumbers = $('[name="phone_numbers"]').val().trim();
        const smsType = $('[name="sms_type"]:checked').val();
        const smsContent = $('[name="sms_content"]').val().trim();
        const isScheduled = $('[name="is_scheduled"]:checked').val();
        const scheduledAt = $('[name="scheduled_at"]').val();

        if (!senderId) {
            e.preventDefault();
            showToast('Please select a sender ID.');
            return false;
        }

        if (!phoneNumbers) {
            e.preventDefault();
            showToast('Please enter at least one phone number.');
            return false;
        } else {
            const numbersArray = phoneNumbers.split(/\r?\n/);
            const numberPattern = /^\d{8,15}$/;
            for (const number of numbersArray) {
                if (!numberPattern.test(number.trim())) {
                    e.preventDefault();
                    showToast(`Invalid phone number detected: ${number}`);
                    return false;
                }
            }
        }

        if (!smsType) {
            e.preventDefault();
            showToast('Please select an SMS type.');
            return false;
        }

        if (!smsContent) {
            e.preventDefault();
            showToast('Please enter SMS content.');
            return false;
        } else if (smsContent.length > 1000) {
            e.preventDefault();
            showToast('SMS content cannot exceed 1000 characters.');
            return false;
        }

        if (isScheduled === '1' && !scheduledAt) {
            e.preventDefault();
            showToast('Please select a scheduled date and time.');
            return false;
        }

        return true;
    });

});
