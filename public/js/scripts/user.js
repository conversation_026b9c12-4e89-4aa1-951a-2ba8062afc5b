"use strict";

// Class definition
var usersAddUser = function () {
    // Shared variables
    const element = document.getElementById('kt_modal_add_user');
    const form = element.querySelector('#kt_modal_add_user_form');
    const modal = new bootstrap.Modal(element);

    // Init add schedule modal
    var initAddUser = () => {

        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/
        var validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'role_id': {
                        validators: {
                            notEmpty: {
                                message: 'User type is required'
                            }
                        }
                    },
                    'name': {
                        validators: {
                            notEmpty: {
                                message: 'Full name is required'
                            }
                        }
                    },
                    'email': {
                        validators: {
                            notEmpty: {
                                message: 'Valid email address is required'
                            }
                        }
                    },
                    'phone': {
                        validators: {
                            notEmpty: {
                                message: 'Valid phone number is required'
                            }
                        }
                    },
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        // Submit button handler
        const submitButton = element.querySelector('[data-kt-users-modal-action="submit"]');
        submitButton.addEventListener('click', e => {
            e.preventDefault();

            // Validate form before submit
            if (validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        // Show loading indication
                        submitButton.setAttribute('data-kt-indicator', 'on');

                        // Disable button to avoid multiple click
                        submitButton.disabled = true;

                        // Create a FormData object to collect form data
                        const formData = new FormData(form);
                        const formDataArray = [];

                        formData.forEach((value, key) => {
                            formDataArray.push([key, value]);
                        });
                        const formDataObject = Object.fromEntries(formDataArray);

                        Request('/users',
                            'POST',
                            formDataObject,
                            () => {
                                // Disable button to avoid multiple click
                                submitButton.disabled = false;
                                // Show loading indication
                                submitButton.setAttribute('data-kt-indicator', 'false');

                                // Show popup confirmation
                                Swal.fire({
                                    text: "User has been successfully created!",
                                    icon: "success",
                                    buttonsStyling: false,
                                    confirmButtonText: "Ok, got it!",
                                    customClass: {
                                        confirmButton: "btn btn-primary"
                                    }
                                }).then(function (result) {
                                    if (result.isConfirmed) {
                                        modal.hide();
                                        if (!window.dataTable) {
                                            window.location.href = window.location.href;
                                        }

                                        window.dataTable.draw();
                                    }
                                });

                            }, (e) => {
                                // Disable button to avoid multiple click
                                submitButton.disabled = false;
                                // Show loading indication
                                submitButton.setAttribute('data-kt-indicator', 'false');

                                Swal.fire({
                                    text: e.responseJSON.message,
                                    icon: "warning"
                                });
                            }
                        );
                    }
                });
            }
        });
    }

    return {
        // Public functions
        init: function () {
            initAddUser();
        }
    };
}();

// On document ready
KTUtil.onDOMContentLoaded(function () {
    usersAddUser.init();
});
