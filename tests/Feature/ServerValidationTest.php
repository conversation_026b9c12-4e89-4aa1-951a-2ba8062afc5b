<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\GatewayProviderType;
use App\Http\Requests\StoreServerRequest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;

class ServerValidationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create([
            'role_id' => 1 // Admin role
        ]);

        // Create a test gateway provider type
        $this->gatewayType = GatewayProviderType::create([
            'name' => 'test_provider',
            'display_name' => 'Test Provider',
            'description' => 'Test provider for validation',
            'auth_method' => 'api_key',
            'http_method' => 'POST',
            'api_parameters_template' => ['to' => '{to}', 'message' => '{message}'],
            'headers_template' => ['Content-Type' => 'application/json'],
            'supported_message_types' => ['text'],
            'is_active' => true,
            'is_built_in' => false
        ]);
    }

    /** @test */
    public function it_requires_authentication_credentials()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled'
        ], $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('api_key'));
    }

    /** @test */
    public function it_accepts_api_key_authentication()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled',
            'api_key' => 'test-api-key-123'
        ], $request->rules());

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function it_accepts_username_password_authentication()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled',
            'username' => 'testuser',
            'password' => 'testpass'
        ], $request->rules());

        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function it_requires_password_when_username_provided()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled',
            'username' => 'testuser'
            // Missing password
        ], $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('password'));
    }

    /** @test */
    public function it_requires_username_when_password_provided()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled',
            'password' => 'testpass'
            // Missing username
        ], $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertTrue($validator->errors()->has('username'));
    }

    /** @test */
    public function it_accepts_both_api_key_and_username_password()
    {
        $request = new StoreServerRequest();
        $validator = Validator::make([
            'name' => 'Test Server',
            'api_link' => 'https://api.test.com/sms',
            'gateway_type' => 'test_provider',
            'status' => 'enabled',
            'api_key' => 'test-api-key-123',
            'username' => 'testuser',
            'password' => 'testpass'
        ], $request->rules());

        $this->assertFalse($validator->fails());
    }
}
