<?php

use App\Models\Company;
use App\Models\Domain;
use App\Models\User;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => 'super-admin']);
    Role::create(['name' => 'master-reseller']);
    Role::create(['name' => 'reseller']);
    Role::create(['name' => 'client']);
    
    // Create companies
    $this->company1 = Company::create([
        'name' => 'Company 1',
        'company_id' => null,
        'status' => 'enabled',
        'current_balance' => 1000,
    ]);
    
    $this->company2 = Company::create([
        'name' => 'Company 2', 
        'company_id' => null,
        'status' => 'enabled',
        'current_balance' => 1000,
    ]);
    
    // Create users with different roles
    $this->superAdmin = User::factory()->create(['company_id' => $this->company1->id]);
    $this->superAdmin->assignRole('super-admin');
    $this->superAdmin->companies()->attach($this->company1->id);
    
    $this->reseller = User::factory()->create(['company_id' => $this->company1->id]);
    $this->reseller->assignRole('reseller');
    $this->reseller->companies()->attach($this->company1->id);
    
    $this->client = User::factory()->create(['company_id' => $this->company1->id]);
    $this->client->assignRole('client');
    $this->client->companies()->attach($this->company1->id);
    
    $this->resellerCompany2 = User::factory()->create(['company_id' => $this->company2->id]);
    $this->resellerCompany2->assignRole('reseller');
    $this->resellerCompany2->companies()->attach($this->company2->id);
});

test('super admin can access domain management page', function () {
    $response = $this->actingAs($this->superAdmin)->get('/account/domains');
    
    $response->assertStatus(200);
    $response->assertViewIs('account.domains');
});

test('reseller can access domain management page', function () {
    $response = $this->actingAs($this->reseller)->get('/account/domains');
    
    $response->assertStatus(200);
    $response->assertViewIs('account.domains');
});

test('client cannot access domain management page', function () {
    $response = $this->actingAs($this->client)->get('/account/domains');
    
    $response->assertStatus(403);
});

test('reseller can add domain', function () {
    $response = $this->actingAs($this->reseller)->post('/account/domains', [
        'domain' => 'example.com'
    ]);
    
    $response->assertRedirect('/account/domains');
    $response->assertSessionHas('success');
    
    $this->assertDatabaseHas('domains', [
        'domain' => 'example.com',
        'company_id' => $this->company1->id
    ]);
});

test('client cannot add domain', function () {
    $response = $this->actingAs($this->client)->post('/account/domains', [
        'domain' => 'example.com'
    ]);
    
    $response->assertStatus(403);
    
    $this->assertDatabaseMissing('domains', [
        'domain' => 'example.com'
    ]);
});

test('domain validation works correctly', function () {
    $response = $this->actingAs($this->reseller)->post('/account/domains', [
        'domain' => '' // Empty domain
    ]);
    
    $response->assertSessionHasErrors(['domain']);
});

test('duplicate domain validation works', function () {
    // Create a domain first
    Domain::create([
        'domain' => 'existing.com',
        'company_id' => $this->company1->id
    ]);
    
    $response = $this->actingAs($this->reseller)->post('/account/domains', [
        'domain' => 'existing.com'
    ]);
    
    $response->assertSessionHasErrors(['domain']);
});

test('tenant isolation works - users only see their company domains', function () {
    // Create domains for both companies
    $domain1 = Domain::create([
        'domain' => 'company1.com',
        'company_id' => $this->company1->id
    ]);
    
    $domain2 = Domain::create([
        'domain' => 'company2.com', 
        'company_id' => $this->company2->id
    ]);
    
    // Company 1 reseller should only see company 1 domains
    $response = $this->actingAs($this->reseller)->get('/account/domains');
    $response->assertSee('company1.com');
    $response->assertDontSee('company2.com');
    
    // Company 2 reseller should only see company 2 domains
    $response = $this->actingAs($this->resellerCompany2)->get('/account/domains');
    $response->assertSee('company2.com');
    $response->assertDontSee('company1.com');
});

test('user cannot delete domain from another company', function () {
    // Create domain for company 2
    $domain = Domain::create([
        'domain' => 'company2.com',
        'company_id' => $this->company2->id
    ]);
    
    // Company 1 reseller tries to delete company 2 domain
    $response = $this->actingAs($this->reseller)->delete("/account/domains/{$domain->id}");
    
    $response->assertRedirect('/account/domains');
    $response->assertSessionHas('error');
    
    // Domain should still exist
    $this->assertDatabaseHas('domains', [
        'id' => $domain->id,
        'domain' => 'company2.com'
    ]);
});

test('user can delete their own company domain', function () {
    // Create domain for company 1
    $domain = Domain::create([
        'domain' => 'company1.com',
        'company_id' => $this->company1->id
    ]);
    
    $response = $this->actingAs($this->reseller)->delete("/account/domains/{$domain->id}");
    
    $response->assertRedirect('/account/domains');
    $response->assertSessionHas('success');
    
    // Domain should be deleted
    $this->assertDatabaseMissing('domains', [
        'id' => $domain->id
    ]);
});

test('domain management tab is visible for vendor roles', function () {
    $response = $this->actingAs($this->reseller)->get('/account/overview');
    
    $response->assertSee('Domains');
});

test('domain management tab is not visible for client role', function () {
    $response = $this->actingAs($this->client)->get('/account/overview');
    
    $response->assertDontSee('Domains');
});
