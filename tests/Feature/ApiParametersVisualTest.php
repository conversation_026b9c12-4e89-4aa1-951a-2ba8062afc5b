<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApiParametersVisualTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_shows_api_parameters_editor_in_create_form()
    {
        // Test that the create form contains the API parameters editor
        $response = $this->get(route('admin.servers.create'));
        
        // Check for the API parameters section
        $response->assertSee('api_parameters_section');
        $response->assertSee('API Parameters');
        $response->assertSee('Available Placeholders');
        $response->assertSee('Parameter Name');
        $response->assertSee('Parameter Value');
        $response->assertSee('Add Parameter');
        $response->assertSee('JSON Preview');
        
        // Check for placeholder examples
        $response->assertSee('{api_key}');
        $response->assertSee('{to}');
        $response->assertSee('{message}');
        $response->assertSee('{sender}');
    }

    /** @test */
    public function it_includes_api_parameters_javascript_functionality()
    {
        $response = $this->get(route('admin.servers.create'));
        
        // Check for JavaScript functions
        $response->assertSee('initializeApiParameters');
        $response->assertSee('addParameterRow');
        $response->assertSee('updateJsonPreview');
        $response->assertSee('showPlaceholderDropdown');
        $response->assertSee('insertPlaceholder');
    }

    /** @test */
    public function it_shows_api_parameters_section_only_for_custom_gateway()
    {
        $response = $this->get(route('admin.servers.create'));
        
        // The API parameters section should be hidden by default
        $response->assertSee('style="display: none;"', false);
        
        // JavaScript should show/hide based on gateway type selection
        $response->assertSee("if (selectedValue === 'custom'");
        $response->assertSee("$('#api_parameters_section').show()");
        $response->assertSee("$('#api_parameters_section').hide()");
    }

    /** @test */
    public function it_provides_comprehensive_placeholder_options()
    {
        $response = $this->get(route('admin.servers.create'));
        
        // Check for all expected placeholders in JavaScript
        $placeholders = [
            '{api_key}', '{username}', '{password}', '{bearer_token}',
            '{to}', '{sender}', '{message}', '{type}', '{type_mapped}',
            '{timestamp}', '{batch_id}', '{company_id}'
        ];
        
        foreach ($placeholders as $placeholder) {
            $response->assertSee($placeholder);
        }
    }

    /** @test */
    public function it_includes_parameter_row_template()
    {
        $response = $this->get(route('admin.servers.create'));
        
        // Check for the parameter row template
        $response->assertSee('parameter_row_template');
        $response->assertSee('parameter-row');
        $response->assertSee('parameter-key');
        $response->assertSee('parameter-value');
        $response->assertSee('remove-parameter');
    }

    /** @test */
    public function it_has_proper_form_structure_for_api_parameters()
    {
        $response = $this->get(route('admin.servers.create'));
        
        // Check for hidden input that will store the JSON
        $response->assertSee('name="api_parameters"');
        $response->assertSee('id="api_parameters_json"');
        $response->assertSee('type="hidden"');
        
        // Check for JSON preview area
        $response->assertSee('id="json_preview"');
    }
}
