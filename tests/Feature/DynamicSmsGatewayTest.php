<?php

namespace Tests\Feature;

use App\Http\Services\SmsServers\SmsSendingBase;
use App\Http\Services\SmsServers\DynamicSmsGateway;
use App\Models\Server;
use App\Models\GatewayProviderType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class DynamicSmsGatewayTest extends TestCase
{
    use RefreshDatabase;

    protected function getEnvironmentSetUp($app)
    {
        // Use SQLite for testing
        $app['config']->set('database.default', 'sqlite');
        $app['config']->set('database.connections.sqlite', [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]);
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Seed gateway provider types
        $this->artisan('db:seed', ['--class' => 'GatewayProviderTypeSeeder']);
    }

    /** @test */
    public function it_can_create_dynamic_gateway_server()
    {
        $providerType = GatewayProviderType::where('name', 'twilio')->first();

        $serverData = [
            'name' => 'Test Twilio Server',
            'api_link' => 'https://api.twilio.com/2010-04-01/Accounts/test/Messages.json',
            'gateway_type' => 'twilio',
            'username' => 'test_account_sid',
            'password' => 'test_auth_token',
            'status' => 'active',
            'sending_limit' => 100,
            'time_base' => 1,
            'time_unit' => 'hour',
        ];

        $server = Server::create(array_merge($serverData, [
            'user_id' => $this->user->id,
            'auth_method' => $providerType->auth_method,
            'http_method' => $providerType->http_method,
            'api_parameters' => $providerType->api_parameters_template,
            'headers' => $providerType->headers_template,
            'success_response_pattern' => $providerType->success_response_pattern,
            'error_response_pattern' => $providerType->error_response_pattern,
            'supported_message_types' => $providerType->supported_message_types,
            'is_legacy' => false
        ]));

        $this->assertDatabaseHas('servers', [
            'name' => 'Test Twilio Server',
            'gateway_type' => 'twilio',
            'auth_method' => 'basic_auth',
            'http_method' => 'POST',
            'is_legacy' => false
        ]);
    }

    /** @test */
    public function it_can_instantiate_dynamic_gateway_for_new_providers()
    {
        $server = Server::factory()->create([
            'gateway_type' => 'twilio',
            'is_legacy' => false,
            'auth_method' => 'basic_auth',
            'http_method' => 'POST'
        ]);

        $gateway = SmsSendingBase::getServerInstance('Twilio', $server);

        $this->assertInstanceOf(DynamicSmsGateway::class, $gateway);
    }

    /** @test */
    public function it_can_instantiate_legacy_gateway_for_existing_providers()
    {
        $server = Server::factory()->create([
            'name' => 'RouteMobile',
            'gateway_type' => 'route_mobile',
            'is_legacy' => true,
            'auth_method' => 'username_password',
            'http_method' => 'GET'
        ]);

        $gateway = SmsSendingBase::getServerInstance('RouteMobile', $server);

        // Should instantiate the legacy RouteMobile class if it exists
        // Otherwise falls back to DynamicSmsGateway
        $this->assertNotNull($gateway);
    }

    /** @test */
    public function it_can_get_auth_credentials_based_on_method()
    {
        $server = Server::factory()->create([
            'auth_method' => 'api_key',
            'api_key' => 'test_api_key_123'
        ]);

        $credentials = $server->getAuthCredentials();

        $this->assertEquals(['api_key' => 'test_api_key_123'], $credentials);
    }

    /** @test */
    public function it_can_get_username_password_credentials()
    {
        $server = Server::factory()->create([
            'auth_method' => 'username_password',
            'username' => 'test_user',
            'password' => 'test_pass'
        ]);

        $credentials = $server->getAuthCredentials();

        $this->assertEquals([
            'username' => 'test_user',
            'password' => 'test_pass'
        ], $credentials);
    }

    /** @test */
    public function it_can_replace_placeholders_in_api_parameters()
    {
        $server = Server::factory()->create([
            'auth_method' => 'api_key',
            'api_key' => 'test_key_123',
            'api_parameters' => [
                'api_key' => '{api_key}',
                'to' => '{to}',
                'message' => '{message}',
                'from' => '{sender}'
            ]
        ]);

        $messageData = [
            'to' => '+**********',
            'message' => 'Test message',
            'sender' => 'TestSender'
        ];

        $parameters = $server->getApiParameters($messageData);

        $this->assertEquals([
            'api_key' => 'test_key_123',
            'to' => '+**********',
            'message' => 'Test message',
            'from' => 'TestSender'
        ], $parameters);
    }

    /** @test */
    public function it_can_check_supported_message_types()
    {
        $server = Server::factory()->create([
            'supported_message_types' => ['text', 'unicode', 'flash']
        ]);

        $this->assertTrue($server->supportsMessageType('text'));
        $this->assertTrue($server->supportsMessageType('unicode'));
        $this->assertTrue($server->supportsMessageType('flash'));
        $this->assertFalse($server->supportsMessageType('binary'));
    }

    /** @test */
    public function it_can_send_sms_via_dynamic_gateway()
    {
        Http::fake([
            'api.twilio.com/*' => Http::response([
                'sid' => 'test_message_sid',
                'status' => 'queued',
                'to' => '+**********',
                'from' => 'TestSender',
                'body' => 'Test message'
            ], 201)
        ]);

        $server = Server::factory()->create([
            'api_link' => 'https://api.twilio.com/2010-04-01/Accounts/test/Messages.json',
            'gateway_type' => 'twilio',
            'auth_method' => 'basic_auth',
            'http_method' => 'POST',
            'username' => 'test_sid',
            'password' => 'test_token',
            'api_parameters' => [
                'From' => '{sender}',
                'To' => '{to}',
                'Body' => '{message}'
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'success_response_pattern' => '"status":"queued"',
            'is_legacy' => false
        ]);

        $gateway = new DynamicSmsGateway();
        SmsSendingBase::$server = $server;

        $result = $gateway->send('+**********', 'Test message', 'TestSender', 'text');

        $this->assertFalse($result->error);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.twilio.com/2010-04-01/Accounts/test/Messages.json' &&
                   $request->method() === 'POST' &&
                   $request->data()['From'] === 'TestSender' &&
                   $request->data()['To'] === '+**********' &&
                   $request->data()['Body'] === 'Test message';
        });
    }
}
