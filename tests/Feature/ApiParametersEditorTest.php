<?php

namespace Tests\Feature;

use App\Models\Server;
use App\Models\User;
use App\Models\Company;
use App\Models\GatewayProviderType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiParametersEditorTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Company $company;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test company and user
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create([
            'company_id' => $this->company->id,
            'role' => 'admin'
        ]);
        
        // Seed gateway provider types
        $this->seed(\Database\Seeders\GatewayProviderTypeSeeder::class);
    }

    /** @test */
    public function it_can_create_server_with_custom_api_parameters()
    {
        $this->actingAs($this->user);

        $customParameters = [
            'auth_token' => '{api_key}',
            'recipient' => '{to}',
            'text_message' => '{message}',
            'sender_name' => '{sender}',
            'msg_type' => '{type}'
        ];

        $response = $this->post(route('admin.servers.store'), [
            'name' => 'Custom SMS Provider',
            'api_link' => 'https://api.customprovider.com/send',
            'gateway_type' => 'custom',
            'api_key' => 'test-api-key-123',
            'status' => 'enabled',
            'api_parameters' => json_encode($customParameters)
        ]);

        $response->assertRedirect(route('admin.servers.index'));
        $response->assertSessionHas('success', 'Server created successfully.');

        // Verify server was created with correct api_parameters
        $server = Server::where('name', 'Custom SMS Provider')->first();
        $this->assertNotNull($server);
        $this->assertEquals($customParameters, $server->api_parameters);
        $this->assertEquals('custom', $server->gateway_type);
    }

    /** @test */
    public function it_can_update_server_with_modified_api_parameters()
    {
        $this->actingAs($this->user);

        // Create a server first
        $server = Server::factory()->create([
            'user_id' => $this->user->id,
            'gateway_type' => 'custom',
            'api_parameters' => [
                'api_key' => '{api_key}',
                'to' => '{to}',
                'message' => '{message}'
            ]
        ]);

        $updatedParameters = [
            'auth_token' => '{api_key}',
            'destination' => '{to}',
            'content' => '{message}',
            'source' => '{sender}',
            'format' => 'json'
        ];

        $response = $this->patch(route('admin.servers.update', $server), [
            'name' => $server->name,
            'api_link' => $server->api_link,
            'gateway_type' => 'custom',
            'api_key' => $server->api_key,
            'status' => $server->status,
            'api_parameters' => json_encode($updatedParameters)
        ]);

        $response->assertRedirect(route('admin.servers.index'));

        // Verify server was updated with new api_parameters
        $server->refresh();
        $this->assertEquals($updatedParameters, $server->api_parameters);
    }

    /** @test */
    public function it_validates_api_parameters_as_valid_json()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('admin.servers.store'), [
            'name' => 'Invalid JSON Server',
            'api_link' => 'https://api.example.com/send',
            'gateway_type' => 'custom',
            'api_key' => 'test-key',
            'status' => 'enabled',
            'api_parameters' => 'invalid-json-string'
        ]);

        $response->assertSessionHasErrors(['api_parameters']);
    }

    /** @test */
    public function it_processes_api_parameters_with_placeholders_correctly()
    {
        $this->actingAs($this->user);

        $server = Server::factory()->create([
            'user_id' => $this->user->id,
            'gateway_type' => 'custom',
            'api_key' => 'test-api-key-123',
            'api_parameters' => [
                'token' => '{api_key}',
                'destination' => '{to}',
                'content' => '{message}',
                'source' => '{sender}'
            ]
        ]);

        $messageData = [
            'to' => '+1234567890',
            'message' => 'Test message',
            'sender' => 'TestSender'
        ];

        $processedParams = $server->getApiParameters($messageData);

        $expectedParams = [
            'token' => 'test-api-key-123',
            'destination' => '+1234567890',
            'content' => 'Test message',
            'source' => 'TestSender'
        ];

        $this->assertEquals($expectedParams, $processedParams);
    }

    /** @test */
    public function it_shows_api_parameters_section_only_for_custom_gateway()
    {
        $this->actingAs($this->user);

        // Test create form
        $response = $this->get(route('admin.servers.create'));
        $response->assertOk();
        $response->assertSee('api_parameters_section');
        $response->assertSee('Available Placeholders');

        // Test edit form with custom gateway
        $customServer = Server::factory()->create([
            'user_id' => $this->user->id,
            'gateway_type' => 'custom'
        ]);

        $response = $this->get(route('admin.servers.edit', $customServer));
        $response->assertOk();
        $response->assertSee('api_parameters_section');
    }
}
