<?php

namespace Tests\Feature;

use App\Models\Server;
use App\Models\User;
use App\Models\Company;
use App\Models\GatewayProviderType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Tests\TestCase;

class ApiParametersEditorTest extends TestCase
{
    use RefreshDatabase, WithoutMiddleware;

    private User $user;
    private Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test company and user
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create([
            'company_id' => $this->company->id
        ]);

        // Seed gateway provider types
        $this->seed(\Database\Seeders\GatewayProviderTypeSeeder::class);
    }

    /** @test */
    public function it_can_create_server_with_custom_api_parameters()
    {
        $this->actingAs($this->user);

        $customParameters = [
            'auth_token' => '{api_key}',
            'recipient' => '{to}',
            'text_message' => '{message}',
            'sender_name' => '{sender}',
            'msg_type' => '{type}'
        ];

        $response = $this->post(route('admin.servers.store'), [
            'name' => 'Custom SMS Provider',
            'api_link' => 'https://api.customprovider.com/send',
            'gateway_type' => 'custom',
            'api_key' => 'test-api-key-123',
            'status' => 'enabled',
            'api_parameters' => json_encode($customParameters)
        ]);

        $response->assertRedirect(route('admin.servers.index'));
        $response->assertSessionHas('success', 'Server created successfully.');

        // Verify server was created with correct api_parameters
        $server = Server::where('name', 'Custom SMS Provider')->first();
        $this->assertNotNull($server);
        $this->assertEquals($customParameters, $server->api_parameters);
        $this->assertEquals('custom', $server->gateway_type);
    }

    /** @test */
    public function it_can_update_server_with_modified_api_parameters()
    {
        $this->actingAs($this->user);

        // Create a server first
        $server = Server::factory()->custom()->create([
            'user_id' => $this->user->id,
            'api_parameters' => [
                'api_key' => '{api_key}',
                'to' => '{to}',
                'message' => '{message}'
            ]
        ]);

        $updatedParameters = [
            'auth_token' => '{api_key}',
            'destination' => '{to}',
            'content' => '{message}',
            'source' => '{sender}',
            'format' => 'json'
        ];

        $response = $this->patch(route('admin.servers.update', $server), [
            'name' => $server->name,
            'api_link' => $server->api_link,
            'gateway_type' => 'custom',
            'api_key' => $server->api_key,
            'status' => $server->status,
            'api_parameters' => json_encode($updatedParameters)
        ]);

        $response->assertRedirect(route('admin.servers.index'));

        // Verify server was updated with new api_parameters
        $server->refresh();

        // Debug: Let's see what we actually got
        $this->assertNotEquals($server->api_parameters, [
            'api_key' => '{api_key}',
            'to' => '{to}',
            'message' => '{message}'
        ], 'Server parameters should have been updated');

        // The parameters should contain the new values
        $this->assertArrayHasKey('auth_token', $server->api_parameters);
        $this->assertArrayHasKey('destination', $server->api_parameters);
        $this->assertArrayHasKey('content', $server->api_parameters);
    }

    /** @test */
    public function it_validates_api_parameters_as_valid_json()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('admin.servers.store'), [
            'name' => 'Invalid JSON Server',
            'api_link' => 'https://api.example.com/send',
            'gateway_type' => 'custom',
            'api_key' => 'test-key',
            'status' => 'enabled',
            'api_parameters' => 'invalid-json-string'
        ]);

        $response->assertSessionHasErrors(['api_parameters']);
    }

    /** @test */
    public function it_stores_api_parameters_correctly_in_database()
    {
        $customParameters = [
            'token' => '{api_key}',
            'destination' => '{to}',
            'content' => '{message}',
            'source' => '{sender}'
        ];

        $server = Server::factory()->custom()->create([
            'user_id' => $this->user->id,
            'api_key' => 'test-api-key-123',
            'api_parameters' => $customParameters
        ]);

        // Verify server was created with correct api_parameters
        $this->assertNotNull($server);
        $this->assertEquals($customParameters, $server->api_parameters);
        $this->assertEquals('custom', $server->gateway_type);
    }

    /** @test */
    public function it_can_update_api_parameters_on_existing_server()
    {
        // Create a server with initial parameters
        $server = Server::factory()->custom()->create([
            'user_id' => $this->user->id,
            'api_parameters' => [
                'api_key' => '{api_key}',
                'to' => '{to}',
                'message' => '{message}'
            ]
        ]);

        $updatedParameters = [
            'auth_token' => '{api_key}',
            'destination' => '{to}',
            'content' => '{message}',
            'source' => '{sender}',
            'format' => 'json'
        ];

        // Update the server with new parameters
        $server->update(['api_parameters' => $updatedParameters]);
        $server->refresh();

        // Verify server was updated with new api_parameters
        $this->assertEquals($updatedParameters, $server->api_parameters);
    }
}
