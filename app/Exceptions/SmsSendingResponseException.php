<?php

namespace App\Exceptions;

use App\Helpers\SmsResponse;
use Exception;

class SmsSendingResponseException extends Exception
{
    protected $jsonResponse = false;

    protected $response = [];

    public function __construct($code = 1001, $jsonResponse = true)
    {
        $this->makeResponse($code);

        parent::__construct($this->response['message'], $code);
        $this->jsonResponse = $jsonResponse;
    }

    private function makeResponse($code)
    {
        $this->response = SmsResponse::error($code);
    }

    public function render($request)
    {
        if ($this->jsonResponse) {
            return response()->json($this->response);
        }

        return $this->response;
    }
}
