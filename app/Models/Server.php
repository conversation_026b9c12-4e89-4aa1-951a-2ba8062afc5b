<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Server extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_link',
        'name',
        'gateway_type',
        'gateway_config',
        'auth_method',
        'http_method',
        'api_parameters',
        'headers',
        'success_response_pattern',
        'error_response_pattern',
        'supported_message_types',
        'is_legacy',
        'api_key',
        'username',
        'password',
        'sending_limit',
        'time_base',
        'time_unit',
        'enable_service',
        'user_id',
        'status',
    ];

    protected $casts = [
        'gateway_config' => 'array',
        'api_parameters' => 'array',
        'headers' => 'array',
        'supported_message_types' => 'array',
        'is_legacy' => 'boolean',
    ];

    /**
     * Get the default sender for the server.
     */
    public function defaultSender(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Sender::class)
            ->where('is_server_default', true);
    }

    /**
     * Get all senders associated with the server.
     */
    public function senders()
    {
        return $this->hasMany(Sender::class);
    }

    /**
     * Get the gateway provider type for this server
     */
    public function gatewayProviderType()
    {
        return $this->belongsTo(GatewayProviderType::class, 'gateway_type', 'name');
    }

    /**
     * Check if this server uses legacy configuration
     */
    public function isLegacy(): bool
    {
        return $this->is_legacy ?? false;
    }

    /**
     * Get the authentication credentials based on auth method
     */
    public function getAuthCredentials(): array
    {
        switch ($this->auth_method) {
            case 'api_key':
                return ['api_key' => $this->api_key];
            case 'username_password':
                return ['username' => $this->username, 'password' => $this->password];
            case 'bearer_token':
                return ['bearer_token' => $this->api_key]; // Reuse api_key field for bearer token
            case 'basic_auth':
                return ['username' => $this->username, 'password' => $this->password];
            default:
                return $this->gateway_config ?? [];
        }
    }

    /**
     * Get supported message types for this server
     */
    public function getSupportedMessageTypes(): array
    {
        return $this->supported_message_types ?? ['text'];
    }

    /**
     * Check if server supports a specific message type
     */
    public function supportsMessageType(string $type): bool
    {
        return in_array($type, $this->getSupportedMessageTypes());
    }

    /**
     * Get API parameters with placeholders replaced
     */
    public function getApiParameters(array $messageData = []): array
    {
        $parameters = $this->api_parameters ?? [];
        $credentials = $this->getAuthCredentials();

        // Merge credentials and message data for placeholder replacement
        $replacements = array_merge($credentials, $messageData);

        // Replace placeholders in parameters
        foreach ($parameters as $key => $value) {
            if (is_string($value)) {
                foreach ($replacements as $placeholder => $replacement) {
                    $value = str_replace('{' . $placeholder . '}', $replacement, $value);
                }
                $parameters[$key] = $value;
            }
        }

        return $parameters;
    }

    /**
     * Get headers with placeholders replaced
     */
    public function getHeaders(array $messageData = []): array
    {
        $headers = $this->headers ?? [];
        $credentials = $this->getAuthCredentials();

        // Merge credentials and message data for placeholder replacement
        $replacements = array_merge($credentials, $messageData);

        // Replace placeholders in headers
        foreach ($headers as $key => $value) {
            if (is_string($value)) {
                foreach ($replacements as $placeholder => $replacement) {
                    $value = str_replace('{' . $placeholder . '}', $replacement, $value);
                }
                $headers[$key] = $value;
            }
        }

        return $headers;
    }
}
