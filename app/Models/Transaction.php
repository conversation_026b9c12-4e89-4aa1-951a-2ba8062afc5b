<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use ForTenants;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'company_id',
        'amount_in',
        'amount_out',
        'date',
        'remarks',
        'log',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
