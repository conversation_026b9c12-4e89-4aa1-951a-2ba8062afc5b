<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    use ForTenants;
    use HasFactory;

    protected $fillable = [
        'sender_id',
        'server_id',
        'phone_number',
        'sms_type',
        'sms_content',
        'sms_count',
        'sms_cost',
        'batch_number',
        'schedule_at',
        'api_response',
        'company_id',
        'user_id',
        'status',
    ];

    public function server()
    {
        return $this->belongsTo(Server::class);
    }

    public function sender()
    {
        return $this->belongsTo(Sender::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
