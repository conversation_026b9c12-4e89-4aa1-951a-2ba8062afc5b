<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Domain extends Model
{
    use ForTenants;
    use HasFactory;

    protected $fillable = [
        'domain',
        'company_id',
        'dns_status',
        'last_verified_at',
        'verification_notes',
        'verification_attempts',
    ];

    protected $casts = [
        'last_verified_at' => 'datetime',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Check if domain is active (DNS properly configured)
     */
    public function isActive(): bool
    {
        return $this->dns_status === 'active';
    }

    /**
     * Check if domain is pending verification
     */
    public function isPending(): bool
    {
        return $this->dns_status === 'pending';
    }

    /**
     * Check if domain verification failed
     */
    public function isFailed(): bool
    {
        return $this->dns_status === 'failed';
    }

    /**
     * Get DNS status badge class for UI
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->dns_status) {
            'active' => 'badge-success',
            'pending' => 'badge-warning',
            'failed' => 'badge-danger',
            default => 'badge-secondary',
        };
    }

    /**
     * Get DNS status display text
     */
    public function getStatusText(): string
    {
        return match($this->dns_status) {
            'active' => 'Active',
            'pending' => 'Pending',
            'failed' => 'Failed',
            default => 'Unknown',
        };
    }
}
