<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    use ForTenants;
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'operator',
        'group_id',
        'company_id',
        'status',
    ];

    public function group()
    {
        return $this->belongsTo(Group::class);
    }
}
