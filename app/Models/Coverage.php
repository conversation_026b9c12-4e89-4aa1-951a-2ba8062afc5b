<?php

namespace App\Models;

use App\Tenant\Traits\ForTenants;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coverage extends Model
{
    use ForTenants;
    use HasFactory;

    protected $fillable = [
        'prefix',
        'masking_price',
        'non_masking_price',
        'operator',
        'server_id',
        'company_id',
        'status',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
