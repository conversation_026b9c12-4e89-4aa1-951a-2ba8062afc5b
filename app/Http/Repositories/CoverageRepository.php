<?php

namespace App\Http\Repositories;

use App\Models\Coverage;
use Illuminate\Database\Eloquent\Model;

class CoverageRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var Coverage
     */
    private $coverage = null;

    public function __construct(?Coverage $coverage = null)
    {
        if (empty($coverage)) {
            $coverage = new Coverage;
        }

        $this->setContext($coverage);
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->coverage->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Store Information
     */
    public function store($data)
    {
        return $this->coverage->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->coverage->update($data);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        return $this->coverage->delete();
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->coverage = $model;

        return $this;
    }
}
