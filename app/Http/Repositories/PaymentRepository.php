<?php

namespace App\Http\Repositories;

use App\Models\Company;
use App\Models\Payment;
use App\Models\User;
use App\Tenant\Manager;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PaymentRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var App\Models\Payment
     */
    private $payment = null;

    /**
     * User instance
     *
     * @var \App\Models\User
     */
    private $user = null;

    /**
     * @var RechargeRepository
     */
    private $rechargeRepo;

    private $company;

    public function __construct(?Payment $payment = null)
    {
        if (empty($payment)) {
            $payment = new Payment;
        }

        $this->user = auth()->user();

        $this->setContext($payment);

        $this->rechargeRepo = new RechargeRepository;

        $this->company = app(Manager::class)->getTenant();
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->payment->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Store Information
     */
    public function store($data)
    {
        $this->payment->amount = $data['recharge_amount'];
        $this->payment->transaction_id = $data['transaction_id'];
        $this->payment->gateway = $data['payment_method'];
        $this->payment->remarks = $data['remarks'] ?? '';
        $this->payment->payment_status = $data['status'];
        $this->payment->company_id = $this->company->id;
        $this->payment->user_id = $this->user->id;
        $this->payment->save();
    }

    public function initiatePayment(Request $request)
    {
        $data = $request->all();
        $data['transaction_id'] = Str::random(10);
        $data['status'] = 'pending';
        $this->store($data);

        $data['payment_id'] = $this->payment->id;
        $data['user_id'] = $this->user->id;
        $this->rechargeRepo->setCompany($this->company)->store($data);
        return $this->payment;
    }

    public function setUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Update Information
     */
    public function update($data) {}

    /**
     * Delete Information
     */
    public function delete()
    {
        $this->payment->delete();
    }

    /**
     * Get Information
     *
     * @param  array  $select
     * @return mixed
     */
    public function getPaymentByTransId($transId, $select = ['*'])
    {
        return $this->payment->where('transaction_id', $transId)->first();
    }

    /**
     * Update payment info
     *
     * @return void
     */
    public function successPayment($data)
    {
        $this->payment->payment_status = 'completed';
        $this->payment->api_response = json_encode($data);
        $this->payment->save();
        //todo: update recharge status
        $this->payment->load('recharge');

        $this->rechargeRepo->setContext($this->payment->recharge)
            ->setCompany($this->payment->company)
            ->successfulRecharge();
    }

    /**
     * Failed payment
     *
     * @return void
     */
    public function failedPayment()
    {
        $this->payment->payment_status = 'failed';
        $this->payment->save();

        $this->payment->load('recharge');

        $this->rechargeRepo->setContext($this->payment->recharge)
            ->failedRecharge();
    }

    /**
     * Canceled payment
     *
     * @return void
     */
    public function canceledPayment()
    {
        $this->payment->payment_status = 'canceled';
        $this->payment->save();

        $this->payment->load('recharge');

        $this->rechargeRepo->setContext($this->payment->recharge)
            ->failedRecharge();
    }

    /**
     * Set model instance
     *
     * @return \App\Models\Payment
     */
    public function setContext(Model $model)
    {
        $this->payment = $model;

        return $this;
    }

    /**
     * Set model instance
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return \App\Models\Recharge
     */
    public function setCompany(Company $company)
    {
        $this->company = $company;

        return $this;
    }
}
