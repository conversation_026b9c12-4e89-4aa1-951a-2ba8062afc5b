<?php

namespace App\Http\Repositories;

use App\Models\Company;
use App\Models\Message;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CompanyRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var \App\Models\Company
     */
    private $company = null;

    public function __construct(?Company $company = null)
    {
        if (empty($company)) {
            $company = new Company;
        }

        $this->setContext($company);
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->company->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Get Information by api_key
     *
     * @param  int  $id
     * @param  array  $select
     * @return \App\Models\Company|null
     */
    public function findByApiKey($key, $select = ['*'])
    {
        return $this->company->select($select)->whereApiKey($key)->first();
    }

    /**
     * Store Information
     */
    public function store($data)
    {
        return $this->company->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->company->update($data);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        return $this->company->delete();
    }

    /**
     * Deduct company balance
     *
     * @param  float  $cost
     * @return \App\Http\Repositories\CompanyRepository
     */
    public function deductBalance($cost)
    {
        $this->company->current_balance -= $cost;
        $this->company->save();

        return $this;
    }

    /**
     * Deduct Balance Recursively
     *
     * @param  array  $companies
     * @return void
     */
    public function deductBalanceRecursively($companies, Message $message)
    {
        $prefix = substr($message->phone_number, 0, 5);
        $price = $message->sender->is_masking ? 'masking_price' : 'non_masking_price';

        Company::whereIn('id', $companies)->update([
            'current_balance' => DB::raw("current_balance - (
                {$message->sms_count} * 
                (SELECT {$price} FROM coverages WHERE coverages.company_id = companies.id AND prefix = '{$prefix}' LIMIT 1)
            )"),
        ]);
    }

    /**
     * Add company balance
     *
     * @param  float  $amount
     * @return \App\Http\Repositories\CompanyRepository
     */
    public function addBalance($amount)
    {
        $this->company->current_balance += $amount;
        $this->company->save();

        return $this;
    }

    /**
     * Set model instance
     *
     * @return \App\Models\Company
     */
    public function setContext(Model $model)
    {
        $this->company = $model;

        return $this;
    }
}
