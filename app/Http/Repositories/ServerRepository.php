<?php

namespace App\Http\Repositories;

use App\Models\Server;
use Illuminate\Database\Eloquent\Model;

class ServerRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var \App\Models\Server
     */
    private $server = null;

    public function __construct(?Server $server = null)
    {
        if (empty($server)) {
            $server = new Server;
        }

        $this->setContext($server);
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->server = $model;

        return $this;
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->server->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     * @return \App\Models\Server|null
     */
    public function get($id, $select = ['*'])
    {
        return $this->server->select($select)->find($id);
    }

    /**
     * Store Information
     */
    public function store($data) {
        return $this->server->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->server->update($data);
    }

    /**
     * Delete Information
     */
    public function delete() {}
}
