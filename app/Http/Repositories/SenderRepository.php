<?php

namespace App\Http\Repositories;

use App\Models\Company;
use App\Models\Sender;
use App\Tenant\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Model;

class SenderRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var \App\Models\Sender
     */
    private $sender = null;

    public function __construct(?Sender $sender = null)
    {
        if (empty($sender)) {
            $sender = new Sender;
        }

        $this->setContext($sender);
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->sender->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     * @return \App\Models\Sender|null
     */
    public function get($id, $select = ['*'])
    {
        return $this->sender->select($select)->find($id);
    }

    public function getWithoutGlobalScopes($id, $select = ['*'])
    {
        return $this->sender->withoutGlobalScopes()->select($select)->find($id);
    }

    public function getDefaultSenders($coverages)
    {
        return $this->sender->withoutGlobalScopes()->where('is_server_default', 1)
            ->whereIn('server_id', collect($coverages)->pluck('server_id')->unique()->toArray())
            ->get()
            ->groupBy('server_id');
    }

    /**
     * Store Information
     */
    public function store($data)
    {
        return $this->sender->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->sender->update($data);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        return $this->sender->delete();
    }

    /**
     * get Sender by sender name Or default
     *
     * @return \App\Models\Sender
     */
    public function getSenderByNameAndCompany($name, Company $company)
    {
        $senders = $this->sender->whereName($name)
            ->orWhere(function ($sender) use ($company) {
                return $sender->whereIn('company_id', [$company->id, 1])
                    ->whereIsDefault(1);
            })
            ->withoutGlobalScope(TenantScope::class)
            ->whereStatus('enabled')
            ->get();

        if ($sender = $senders->where('name', $name)->first()) {
            return $sender;
        }

        if ($sender = $senders->where('company_id', $company->id)->first()) {
            return $sender;
        }

        return $senders->where('company_id', 1)->first();
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->sender = $model;

        return $this;
    }
}
