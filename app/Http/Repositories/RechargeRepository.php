<?php

namespace App\Http\Repositories;

use App\Models\Company;
use App\Models\Recharge;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class RechargeRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var App\Models\Recharge
     */
    private $recharge = null;

    /**
     * @var CompanyRepository
     */
    private $companyRepo;

    /**
     * User instance
     *
     * @var \App\Models\User
     */
    private $user = null;

    private $company = null;

    public function __construct(?Recharge $recharge = null)
    {
        if (empty($recharge)) {
            $recharge = new Recharge;
        }

        $this->user = auth()->user();
        $this->setContext($recharge);

        $this->companyRepo = new CompanyRepository;
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->recharge->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Store Information
     */
    public function store($data)
    {
        $this->recharge->payment_id = $data['payment_id'];
        $this->recharge->recharge_amount = $data['recharge_amount'];
        $this->recharge->balance_before = $this->company->current_balance;
        $this->recharge->balance_after = $this->company->current_balance + $data['recharge_amount'];
        $this->recharge->recharge_date = Carbon::now();
        $this->recharge->status = $data['status'];
        $this->recharge->company_id = $this->company->id;
        $this->recharge->gateway_fee = $data['gateway_fee'];
        $this->recharge->user_id = $data['user_id'];
        $this->recharge->save();
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        //$this->companyRepo->setContext($this->company)->addBalance($data['recharge_amount']);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        $this->recharge->delete();
    }

    /**
     * Set model instance
     *
     * @return \App\Models\Recharge
     */
    public function setContext(Model $model)
    {
        $this->recharge = $model;

        return $this;
    }

    /**
     * Set model instance
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return \App\Models\Recharge
     */
    public function setCompany(Company $company)
    {
        $this->company = $company;

        return $this;
    }

    /**
     * Successful recharge
     *
     * @return void
     */
    public function successfulRecharge()
    {
        $this->recharge->status = 'completed';
        $this->recharge->save();

        $this->companyRepo->setContext($this->company)->addBalance($this->recharge->recharge_amount);
    }

    /**
     * Failed recharge
     *
     * @return void
     */
    public function failedRecharge()
    {
        $this->recharge->status = 'failed';
        $this->recharge->save();
    }

    /**
     * Canceled recharge
     *
     * @return void
     */
    public function canceledRecharge()
    {
        $this->recharge->status = 'canceled';
        $this->recharge->save();
    }
}
