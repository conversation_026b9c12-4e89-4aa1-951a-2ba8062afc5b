<?php

namespace App\Http\Repositories;

use App\Models\Contact;
use Illuminate\Database\Eloquent\Model;

class ContactRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var Contact
     */
    private $contact = null;

    public function __construct(?Contact $contact = null)
    {
        if (empty($contact)) {
            $contact = new Contact;
        }

        $this->setContext($contact);
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->contact->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Store Information
     */
    public function store($data)
    {
        return $this->contact->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->contact->update($data);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        return $this->contact->delete();
    }

    /**
     * Contacts by group
     */
    public function getByGroup($groupId, $selects = ['*'])
    {
        return $this->contact->whereGroupId($groupId)->get($selects);
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->contact = $model;

        return $this;
    }
}
