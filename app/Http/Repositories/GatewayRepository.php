<?php

namespace App\Http\Repositories;

use App\Models\Gateway;
use Illuminate\Database\Eloquent\Model;

class GatewayRepository implements RepositoryInterface
{
    /**
     * Model instance
     *
     * @var \App\Models\Server
     */
    private $gateway = null;

    public function __construct(?Gateway $gateway = null)
    {
        if (empty($gateway)) {
            $gateway = new Gateway;
        }

        $this->setContext($gateway);
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->gateway = $model;

        return $this;
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->gateway->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     * @return \App\Models\Gateway|null
     */
    public function get($id, $select = ['*'])
    {
        return $this->gateway->select($select)->find($id);
    }

    /**
     * Store Information
     */
    public function store($data) {}

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->gateway->update($data);
    }

    /**
     * Delete Information
     */
    public function delete() {}

    /**
     * return gateway by class name
     *
     * @return mixed
     */
    public function findByClassName($gatewayName)
    {
        return $this->gateway->where('class_name', strtolower($gatewayName))->first();
    }
}
