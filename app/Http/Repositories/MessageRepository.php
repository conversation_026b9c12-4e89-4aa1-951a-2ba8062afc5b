<?php

namespace App\Http\Repositories;

use App\Helpers\Format;
use App\Models\Message;
use App\Tenant\Scopes\TenantScope;
use App\Tenant\Traits\TenantHandler;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class MessageRepository implements RepositoryInterface
{
    use TenantHandler;

    /**
     * Model instance
     *
     * @var Message|null
     */
    private $message = null;

    /**
     * Model instance
     *
     * @var \App\Http\Repositories\CompanyRepository|null
     */
    private $companyRepo = null;

    public function __construct(?Message $message = null)
    {
        if (empty($message)) {
            $message = new Message;
        }

        $this->companyRepo = new CompanyRepository;
        $this->setContext($message);
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*']) {}

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     */
    public function get($id, $select = ['*']) {}

    /**
     * Get Sender and Server Information
     *
     * @return mixed
     */
    public function getWithSenderServer($id, $select = ['*'])
    {
        return $this->message
            ->select($select)
            ->with([
                'sender' => function ($query) {
                    $query->withoutGlobalScope(TenantScope::class);
                },
                'server',
            ])
            ->find($id);
    }

    /**
     * Store Information
     */
    public function store($data) {}

    /**
     * Store Information
     *
     * @param  array  $data
     */
    public function storeMany($data)
    {
        $data = array_map(function ($message) {
            $message['created_at'] = Carbon::now();
            $message['updated_at'] = Carbon::now();
            $message['company_id'] = $this->getTenant()->id;

            return $message;
        }, $data);

        return $this->message->insert($data);
    }

    /**
     * Get Pending Messages
     *
     * @param  array  $select
     */
    public function getPendingMessages($select = ['*']): Message
    {
        return $this->message->select($select)
            ->whereStatus('pending')
            ->whereRaw(
                '(SELECT current_balance
                                    FROM companies
                                    WHERE companies.id = messages.company_id
                                    LIMIT 1
                                ) >= sms_cost'
            )
            ->get();
    }

    /**
     * Get Scheduled Messages
     *
     * @param  array  $select
     */
    public function getScheduledMessages($select = ['*']): Message
    {
        return $this->message->select($select)
            ->whereStatus('scheduled')
            ->where('schedule_at', '<=', Carbon::now())
            ->whereRaw(
                '(SELECT current_balance
                                    FROM companies
                                    WHERE companies.id = messages.company_id
                                    LIMIT 1
                                ) >= sms_cost'
            )
            ->get();
    }

    /**
     * Update Message toProcessing
     *
     * @return MessageRepository
     */
    public function updateProcessingSendingMessage(): static
    {
        $this->message->status = 'processing';
        $this->message->save();

        return $this;
    }

    /**
     * Update successful Message
     *
     * @return MessageRepository
     */
    public function updateSuccessFullMessage(string $response): static
    {
        $this->message->status = 'success';
        $this->message->api_response = json_encode($response);
        $this->message->save();

        $this->message->load('company');

        $companies = [$this->message->company->id];

        $parent = $this->message->company->parent;

        if (isset($parent)) {
            Format::getRecursiveParentIds($this->message->company->parent, $companies);
        }

        $this->companyRepo->setContext($this->message->company)
            ->deductBalanceRecursively($companies, $this->message);

        return $this;
    }

    /**
     * Update Failed Message
     *
     * @return MessageRepository
     */
    public function updateFailedSendingMessage(string $response)
    {
        $this->message->status = 'failed';
        $this->message->api_response = json_encode($response);
        $this->message->save();

        return $this;
    }

    /**
     * Get Messages By Batch
     *
     * @param  string  $batchNumber
     * @return Collection
     */
    public function getMessagesByBatch($batchNumber)
    {
        return $this->message->whereBatchNumber($batchNumber)->get();
    }

    /**
     * Update Information
     */
    public function update($data) {}

    /**
     * Delete Information
     */
    public function delete() {}

    /**
     * Set model instance
     *
     * @return MessageRepository
     */
    public function setContext(Model $model): static
    {
        $this->message = $model;

        return $this;
    }
}
