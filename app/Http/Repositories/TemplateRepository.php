<?php

namespace App\Http\Repositories;

use App\Models\Template;
use Illuminate\Database\Eloquent\Model;

class TemplateRepository
{
    /**
     * Model instance
     *
     * @var \App\Models\Template
     */
    private $template = null;

    public function __construct(?Template $template = null)
    {
        if (empty($template)) {
            $template = new Template;
        }

        $this->setContext($template);
    }

    /**
     * Set model instance
     *
     * @return \App\Http\Repositories\RepositoryInterface
     */
    public function setContext(Model $model)
    {
        $this->template = $model;

        return $this;
    }

    /**
     * GetAll Information
     *
     * @param  array  $select
     */
    public function getAll($select = ['*'])
    {
        return $this->template->get($select);
    }

    /**
     * Get Information
     *
     * @param  int  $id
     * @param  array  $select
     * @return \App\Models\Template|null
     */
    public function get($id, $select = ['*'])
    {
        return $this->template->select($select)->find($id);
    }

    /**
     * Store Information
     */
    public function store($data)
    {
        return $this->template->create($data);
    }

    /**
     * Update Information
     */
    public function update($data)
    {
        return $this->template->update($data);
    }

    /**
     * Delete Information
     */
    public function delete()
    {
        return $this->template->delete();
    }
}
