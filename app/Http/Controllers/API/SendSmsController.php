<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreMessageAPIRequest;
use App\Http\Services\SendSmsService;

class SendSmsController extends Controller
{
    private $send;

    public function __construct(SendSmsService $send)
    {
        $this->send = $send;
    }

    /**
     * Display a listing of the resource.
     */
    public function smsSend(StoreMessageAPIRequest $request)
    {
        return $this->send->smsSend($request);
    }
}
