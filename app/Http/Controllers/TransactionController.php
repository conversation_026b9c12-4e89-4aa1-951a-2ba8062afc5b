<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TransactionController extends Controller
{
    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        return datatables()->of(
            Transaction::select('user_id',
                'company_id',
                'amount_in',
                'amount_out',
                'date',
                'remarks',
                'log'
            )
                ->with('company')
                ->orderBy('id', 'DESC')
        )
            ->editColumn('date', function (Transaction $transaction) {
                $formattedDate = Carbon::parse($transaction->date)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($transaction->date)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('reports.transactions');
    }
}
