<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRechargeRequest;
use App\Http\Requests\UpdateRechargeRequest;
use App\Models\Payment;
use App\Models\Recharge;
use App\Tenant\Scopes\TenantScope;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Exceptions\Exception;

class RechargeController extends Controller
{
    /**
     * @throws Exception
     */
    public function browse(Request $request)
    {
        return datatables()->of(
            Recharge::with('payment')->orderBy('id', 'desc')
        )
            ->editColumn('recharge_date', function (Recharge $recharge) {
                if (! isset($recharge->payment)) {
                    return '';
                }
                $formattedDate = Carbon::parse($recharge->payment->created_at)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($recharge->payment->created_at)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->toJson();
    }

    /**
     * @throws Exception
     */
    public function list(Request $request)
    {
        return datatables()->of(
            Payment::withoutGlobalScope(TenantScope::class)
                ->with('company')
                ->select('payments.*')
                ->where('payments.gateway', 'manual')
                ->orderBy('id', 'desc')
        )
            ->addColumn('recharge_date', function (Payment $payment) {
                if (empty($payment->created_at)) {
                    return '';
                }
                $formattedDate = Carbon::parse($payment->created_at)->format('M d, Y h:i A');
                $humanReadable = Carbon::parse($payment->created_at)->diffForHumans();

                return "$formattedDate ($humanReadable)";
            })
            ->editColumn('remarks', function (Payment $payment) {
                return $payment->remarks;
            })
            ->addColumn('recharge_amount', function (Payment $payment) {
                return $payment->amount;
            })
            ->addColumn('action', function (Payment $payment) {
                $action = '';
                if ($payment->payment_status == 'pending') {
                    $action .= ' <a href="'.route('payment.success', [
                        'trans_id' => $payment->transaction_id,
                        'payment_method' => 'manual',
                        'route' => 'recharges',
                    ]).'" class="btn btn-primary btn-sm" >Approved</a>';
                }

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRechargeRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Recharge $recharge)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Recharge $recharge)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRechargeRequest $request, Recharge $recharge)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Recharge $recharge)
    {
        //
    }
}
