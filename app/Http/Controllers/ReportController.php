<?php

namespace App\Http\Controllers;

use App\Models\Message;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function todayDetail(Request $request)
    {
        return datatables()->of(
            Message::select(
                'messages.phone_number AS mobile',
                'messages.schedule_at AS sent_time',
                'senders.name AS sender_name',
                'messages.sms_cost AS charge_per_sms',
                'messages.sms_content AS sms_text',
                'messages.api_response AS api_response',
                'messages.status AS status',
                'messages.created_at AS created_at'
            )
                ->selectRaw('(
                    SELECT operator FROM coverages
                    WHERE coverages.company_id = messages.company_id
                        AND coverages.prefix = LEFT(messages.phone_number, 5)
                ) AS operator')
                ->leftJoin('senders', 'senders.id', 'messages.sender_id')
                ->orderBy('messages.id', 'DESC')
        )
            ->editColumn('sent_time', function (Message $message) {
                $send_time = is_null($message->sent_time) ? $message->created_at : $message->sent_time;

                return Carbon::parse($send_time)->format('M d, Y h:i A');

            })
            ->to<PERSON><PERSON>();
    }
}
