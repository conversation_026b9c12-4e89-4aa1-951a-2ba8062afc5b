<?php

namespace App\Http\Controllers\Auth;

use App\Events\UserRegistered;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\AutoGeneratedPassword;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:'.User::class],
            'phone' => ['required', 'string', 'max:11', 'unique:'.User::class],
        ]);

        if ($validator->fails()) {
            return redirect('/register')
                ->withErrors($validator)
                ->withInput();
        }

        $username = $this->generateUniqueUsername();

        $password = Str::random(12);

        $company_id = 1;

        $user = User::create([
            'name' => $request->name,
            'company_id' => $company_id,
            'phone' => $request->phone,
            'username' => $username,
            'email' => $request->email,
            'password' => Hash::make($password),
        ]);

        $user->assignRole('client');

        event(new UserRegistered($user));

        $user->notify(new AutoGeneratedPassword($username, $password));

        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }

    /**
     * @return string
     */
    private function generateUniqueUsername()
    {
        // Generate a random username
        $username = 'C'.str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);

        // Check if the generated username already exists in the database
        while (User::where('username', $username)->exists()) {
            // If it exists, generate a new one
            $username = 'C'.str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }

        return $username;
    }
}
