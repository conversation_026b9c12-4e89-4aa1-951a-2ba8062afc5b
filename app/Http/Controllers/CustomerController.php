<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Sender;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    /**
     * Display form to assign a masking number to a specific user
     *
     * @param User $user
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application
     */
    public function showAssignForm(User $user)
    {
        // Fetch customers for the dropdown with searchable fields
        $customers = User::select('id', 'name', 'username', 'phone', 'email')->get();

        // Fetch masking numbers with `is_masking = 1` and `company_id` either `null` or `1`
        $maskingNumbers = Sender::where('is_masking', 1)
            ->where(function ($query) {
                $query->whereNull('company_id')
                    ->orWhere('company_id', 1);
            })
            ->with('server:id,name,status')
            ->get(['id', 'server_id', 'name', 'status']);

        return view('customers.assign-masking', compact('user', 'customers', 'maskingNumbers'));
    }


    public function assignMaskingNumber(Request $request)
    {
        $request->validate([
            'sender_id' => 'required|exists:senders,id',
            'user_id' => 'required|exists:users,id'
        ]);

        try {
            DB::beginTransaction();

            // Retrieve the user's company directly
            $user = User::findOrFail($request->user_id);
            $companyId = $user->company->id;

            // Set company_id to null for all senders associated with this company
            Sender::where('company_id', $companyId)
                ->update(['company_id' => null]);

            // Assign the selected sender to the user's company
            $sender = Sender::findOrFail($request->sender_id);
            $sender->company_id = $companyId;
            $sender->save();

            DB::commit();

            return redirect()->route('senders.index')->with('success', 'Masking number assigned successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->withErrors(['error' => 'An error occurred while assigning the masking number. Please try again.']);
        }
    }

}
