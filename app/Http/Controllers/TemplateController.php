<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTemplateRequest;
use App\Http\Requests\UpdateTemplateRequest;
use App\Http\Services\TemplateService;
use App\Models\Template;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Exceptions\Exception;

class TemplateController extends Controller
{
    private $template;

    public function __construct(TemplateService $template)
    {
        $this->template = $template;
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        return datatables()->of(Template::query())
            ->editColumn('created_at', function (Template $template) {
                return Carbon::parse($template->created_at)->format('F j, Y, g:i a');
            })
            ->addColumn('action', function (Template $template) {
                $action = '<button href="#" class="btn btn-danger btn-sm delete-button" data-endpoint="'.route('templates.destroy', $template->id).'" >Delete</button>';
                $action .= ' <a href="'.route('templates.edit', $template->id).'" class="btn btn-secondary btn-sm" >Edit</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    //getTemplate
    public function getTemplate(Request $request): \Illuminate\Http\JsonResponse
    {
        return datatables()->of(Template::query())
            ->editColumn('created_at', function (Template $template) {
                return Carbon::parse($template->created_at)->format('F j, Y, g:i a');
            })
            ->addColumn('action', function (Template $template) {
                return '<button href="#" class="btn btn-secondary btn-sm insert-button" data-text="'.$template->text.'" >Insert</button>';
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('templates.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('templates.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTemplateRequest $request)
    {
        $this->template->store($request);

        return redirect()->route('templates.index')
            ->with('success', 'Template created successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Template $template)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Template $template)
    {
        $user = auth()->user();
        if ($user->hasRole('super-admin') || $template->user_id == $user->id) {
            return view('templates.edit', [
                'template' => $template,
            ]);
        }

        return redirect()->back()->with('error', 'You dont have permission to access.');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTemplateRequest $request, Template $template)
    {
        $this->template->update($request, $template);

        return redirect()->route('templates.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Template $template)
    {
        $user = auth()->user();
        if ($user->hasRole('super-admin') || $template->user_id == $user->id) {
            $this->template->delete($template);

            return response([
                'success' => true,
                'message' => 'Template has been deleted successfully.',
            ]);
        }

        return response([
            'success' => false,
            'message' => 'you dont have permission to access.',
        ], 401);
    }
}
