<?php

namespace App\Http\Controllers;

use App\Exceptions\ErrorMessageException;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Services\PaymentService;
use App\Http\Services\UserService;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    private $user;

    private $payment;

    public function __construct(UserService $user, PaymentService $payment)
    {
        $this->user = $user;
        $this->payment = $payment;
    }

    /**
     * Browse All user using Data Table
     *
     * @throws \Yajra\DataTables\Exceptions\Exception
     * @throws \Exception
     */
    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = auth()->user();

        return datatables()
            ->of(
                User::has('companies')
                    ->with('roles', 'companies')
                    ->when(! $user->hasRole('super-admin'), function ($q) use ($user) {
                        $q->where('parent_id', $user->id);
                    })
                    ->when($user->hasRole('super-admin'), function ($q) use ($user) {
                        $q->where('id', '!=', $user->id);
                    })
                    ->whereHas('roles', function ($query) use ($request) {
                        $query->when($request->role_id, function ($q) use ($request) {
                            return $q->where('id', $request->role_id);
                        });
                    })
                    ->orderBy('id', 'ASC')
            )
            ->addColumn('user_type', function (User $user) {
                $name = $user->roles->first()->name ?? '-';

                return ucfirst(str_replace('-', ' ', $name));
            })
            ->addColumn('current_balance', function (User $user) {
                return $user->companies->first()->current_balance;
            })
            ->addColumn('balance_expired', function (User $user) {
                return Carbon::parse($user->companies->first()->balance_expired)->format('F j, Y, g:i a');
            })
            ->addColumn('action', function (User $user) {
                $action = '<button href="#" class="btn btn-primary btn-sm" data-bs-toggle="modal"
                                data-bs-target="#kt_modal_add_balance" data-user="'.$user->id.'">Credit/Debit</button>';
                $action .= ' <a href="'.route('users.show', $user->id).'" class="btn btn-success btn-sm" >View</a>';

                return $action;
            })
            ->rawColumns(['avatar', 'action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $users_by_role = Role::with('users')->where('name', '!=', 'super-admin')->get();
        // Get the list of roles
        $roles = Role::where('name', '!=', 'super-admin')->pluck('name', 'id');
        // Filter the roles based on the current role
        $roles = collect($roles)->filter(function ($name, $id) use ($user) {
            return $id > $user->roles->first()->id;
        })->map(function ($name) {
            return ucfirst(str_replace('-', ' ', $name));
        })->toArray();

        return view('users.index', compact('roles', 'users_by_role'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        $this->user->store($request);

        return response([
            'success' => true,
            'message' => 'User has been created successfully.',
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user = User::with('roles')->whereId($user->id)->first();
        $logs = AuthenticationLog::where('authenticatable_id', '=', $user->id)->orderBy('login_at', 'DESC')->get();

        $roles = Role::pluck('name', 'id');
        // Filter the roles based on the current role
        $roles = collect($roles)->filter(function ($name, $id) use ($user) {
            return $id > $user->roles->first()->id;
        })->map(function ($name) {
            return ucfirst(str_replace('-', ' ', $name));
        })->toArray();

        return view('users.view', compact('user', 'logs', 'roles'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $this->user->edit($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $data = $request->validate([
            'name' => 'required',
            'email' => 'required',
        ]);

        //$this->user->storeOrUpdate($id, $data);

        return redirect()->route('user.index')->with([
            'message' => 'User updated successfully!',
            'status' => 'success',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $this->user->delete($user);

        return response([
            'success' => true,
            'message' => 'User has been deleted successfully.',
        ]);
    }

    public function getUser(User $user)
    {
        return response([
            'success' => true,
            'data' => $user,
        ]);
    }

    /**
     * @throws ErrorMessageException
     */
    public function balance(Request $request)
    {

        // Todo add validation for request
        // self balance add not possible
        // check add balance permission
        // only under user balance can add.

        $request->request->add([
            'get_payment_info' => true,
        ]);
        $payment = $this->payment->proceedToPayment($request);

        $request->request->add([
            'trans_id' => $payment->transaction_id,
        ]);

        $this->payment->success($request);

        return response([
            'success' => true,
            'message' => 'Done',
        ]);
    }
}
