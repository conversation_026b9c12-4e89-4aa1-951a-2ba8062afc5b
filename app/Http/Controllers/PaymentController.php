<?php

namespace App\Http\Controllers;

use App\Exceptions\ErrorMessageException;
use App\Http\Requests\StoreRechargeRequest;
use App\Http\Services\PaymentService;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    private $payment;

    public function __construct(PaymentService $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Payment proceed method
     *
     * @return mixed
     */
    public function index(StoreRechargeRequest $request)
    {
        return $this->payment->proceedToPayment($request);
    }

    /**
     * @throws ErrorMessageException
     */
    public function success(Request $request)
    {
        // Early return for non-success status
        if ($request->status != 'success') {
            throw new ErrorMessageException('Recharged failed');
        }

        $this->payment->success($request);

        if (! empty($request->route)) {
            return redirect()->route($request->route)->with('success', 'Recharged approved successfully');
        }

        return redirect()->route('account.recharge-history')->with('success', 'Recharge successfully');
    }

    public function fail(Request $request)
    {
        $this->payment->fail($request);

        return redirect()->route('account.recharge-history');
    }

    public function cancel(Request $request)
    {
        $this->payment->canceled($request);

        return redirect()->route('account.recharge-history');
    }

    public function ipn(Request $request)
    {
        $this->payment->ipn($request);
    }
}
