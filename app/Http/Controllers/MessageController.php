<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMessageRequest;
use App\Http\Services\MessageService;
use App\Imports\DynamicData;
use App\Models\Group;
use App\Models\Sender;
use App\Tenant\Manager;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class MessageController extends Controller
{
    private $message;

    protected $manager;

    public function __construct(MessageService $message)
    {
        $this->message = $message;
        $this->manager = app(Manager::class);
    }

    public function store(StoreMessageRequest $request)
    {
        try {
            $response = $this->message->store($request);

            // If sending failed (e.g., no valid numbers)
            if (isset($response['status']) && $response['status'] === 'failed') {
                return redirect()->route('messages.create')
                    ->with('error', $response['message'])
                    ->withInput();
            }

            // Use the service response message or create a default success message
            $apiMessage = $response['message'] ?? 'SMS send request submitted successfully!';

            return redirect()->route('messages.create')
                ->with('success', $apiMessage);

        } catch (\Exception $e) {
            return redirect()->route('messages.create')
                ->with('error', 'Error:'.$e->getMessage())
                ->withInput();
        }
    }

    public function create()
    {
        $tenant = $this->manager->getTenant();
        if (empty($tenant)) {
            return;
        }
        $senders = Sender::withoutGlobalScopes()
            ->where(function ($q) use ($tenant) {
                $q->where('company_id', $tenant->id)
                    ->orWhere('is_global', 1);
            })
            ->whereStatus('Approved')
            ->orderByRaw('company_id = ? desc', [$tenant->id]) // Order by company_id match first
            ->orderBy('is_default', 'asc')
            ->orderBy('name', 'asc')
            ->get(['id', 'name', 'is_default']);

        $groups = Group::get(['id', 'name']);

        // Your logic for sending SMS
        return view('messaging.create', [
            'senders' => $senders,
            'groups' => $groups,
        ]);
    }

    public function getDynamicSms()
    {
        $tenant = $this->manager->getTenant();
        if (empty($tenant)) {
            return;
        }
        $senders = Sender::withoutGlobalScopes()
            ->where(function ($q) use ($tenant) {
                $q->where('company_id', $tenant->id)
                    ->orWhere('is_global', 1);
            })
            ->whereStatus('Approved')
            ->orderByRaw('company_id = ? desc', [$tenant->id]) // Order by company_id match first
            ->orderBy('is_default', 'asc')
            ->orderBy('name', 'asc')
            ->get(['id', 'name', 'is_default']);

        return view('messaging.send-dynamic-sms', [
            'senders' => $senders,
        ]);
    }

    public function campaign()
    {
        // Your logic for handling campaigns
        return view('messaging.campaign');
    }

    public function Sender()
    {
        // Your logic for handling sender IDs
        return view('messaging.sender-id');
    }

    public function templates()
    {
        // Your logic for handling templates
        return view('messaging.templates');
    }

    public function postDynamicSms(Request $request)
    {
        $data = Excel::toArray(new DynamicData, $request->file('file'));

        $this->message->dynamicSms($request, $data);

        return redirect()->route('messages.dynamic-sms.get')
            ->with('success', 'SMS send request submit successfully');
    }

    public function messagesGroupStore(Request $request)
    {
        $this->message->groupSms($request);

        return redirect()->route('messages.create')
            ->with('success', 'SMS send request submit successfully');
    }
}
