<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreContactRequest;
use App\Http\Requests\UpdateContactRequest;
use App\Models\Contact;
use App\Models\Group;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $allContactsCount = Contact::count();
        $groups = Group::withCount('contacts')->get();
        $contacts = Contact::get(['id', 'name', 'phone']);

        return view('contacts.index', [
            'groups' => $groups,
            'contacts' => $contacts,
            'allContactsCount' => $allContactsCount,
        ]);
    }

    /**
     * @throws \Yajra\DataTables\Exceptions\Exception
     */
    public function browse(Request $request)
    {
        return datatables()
            ->of(
                Contact::when($request->operator, function ($q) use ($request) {
                    return $q->where('operator', $request->operator);
                })
                    ->when($request->group, function ($q) use ($request) {
                        return $q->where('group_id', $request->group);
                    })
                    ->when($request->status, function ($q) use ($request) {
                        return $q->where('status', $request->status);
                    })
                    ->with('group')
            )
            ->addColumn('group_name', function (Contact $contact) {
                return isset($contact->group->name) ? $contact->group->name : '';
            })
            ->addColumn('action', function (Contact $contact) {
                $action = '<a href="'.route('contacts.edit', $contact->id).'" class="btn btn-primary btn-sm" >Edit</a> ';
                $action .= '<a href="'.route('contacts.show', $contact->id).'" class="btn btn-secondary btn-sm" >View</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $allContactsCount = Contact::count();
        $groups = Group::withCount('contacts')->get();
        $contacts = Contact::get(['id', 'name', 'phone']);
        $operators = Common::operators();

        return view('contacts.add-contact', [
            'groups' => $groups,
            'contacts' => $contacts,
            'allContactsCount' => $allContactsCount,
            'operators' => $operators,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContactRequest $request)
    {
        Contact::create([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'operator' => $request->operator,
            'group_id' => $request->group_id,
            'status' => $request->status,
        ]);

        return redirect()->route('contacts.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Contact $contact)
    {
        $allContactsCount = Contact::count();
        $groups = Group::withCount('contacts')->get();
        $contacts = Contact::get(['id', 'name', 'phone']);

        return view('contacts.view-contact', [
            'groups' => $groups,
            'contacts' => $contacts,
            'allContactsCount' => $allContactsCount,
            'contact' => $contact,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contact $contact)
    {
        $allContactsCount = Contact::count();
        $groups = Group::withCount('contacts')->get();
        $contacts = Contact::get(['id', 'name', 'phone']);
        $operators = Common::operators();

        return view('contacts.edit-contact', [
            'groups' => $groups,
            'contacts' => $contacts,
            'allContactsCount' => $allContactsCount,
            'contact' => $contact,
            'operators' => $operators,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContactRequest $request, Contact $contact)
    {
        try {
            $contact->update($request->only('name', 'email', 'phone', 'group_id', 'operator', 'status'));

            return redirect()->route('contacts.index')
                ->with('success', 'Contact update successfully');
        } catch (\Exception $e) {
            return redirect()->route('contacts.index')
                ->with('error', 'An error occurred while deleting the contact');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact)
    {
        try {
            $contact->delete();

            return redirect()->route('contacts.index')
                ->with('success', 'Contact deleted successfully');
        } catch (\Exception $e) {
            return redirect()->route('contacts.index')
                ->with('error', 'An error occurred while deleting the contact');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function addGroup(Request $request)
    {
        Group::create([
            'name' => $request->name,
        ]);

        return response([
            'success' => true,
            'message' => 'Group has been created successfully.',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function deleteGroup(Request $request, Group $group)
    {
        if (empty($group)) {
            return response([
                'success' => false,
                'message' => 'Invalid group info has been provided.',
            ], 404);
        }

        $group->delete();

        return response([
            'success' => true,
            'message' => 'Group has been deleted successfully.',
        ]);
    }
}
