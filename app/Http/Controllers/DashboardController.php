<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\Transaction;
use App\Tenant\Manager;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $company = app(Manager::class)->getTenant();

        // Calculate SMS count for last week
        $sms_last_week = Message::where('company_id', $company->id)
            ->whereBetween('created_at', [now()->subWeek(), now()])
            ->count();

        // Calculate cost for last week
        $cost_last_week = Message::where('company_id', $company->id)
            ->whereBetween('created_at', [now()->subWeek(), now()])
            ->sum('sms_cost');

        // Calculate SMS count for last month
        $sms_in_last_month = Message::where('company_id', $company->id)
            ->whereBetween('created_at', [now()->startOfMonth()->subMonth(), now()->endOfMonth()->subMonth()])
            ->count();

        // Calculate cost for last month
        $cost_in_last_month = Message::where('company_id', $company->id)
            ->whereBetween('created_at', [now()->startOfMonth()->subMonth(), now()->endOfMonth()->subMonth()])
            ->sum('sms_cost');

        // Get balance data
        $balance = $company->current_balance;

        // Get SMS quantities
        $masking_sms_quantity = Message::where('company_id', $company->id)->where('sms_type', 'masking')->count();
        $non_masking_sms_quantity = Message::where('company_id', $company->id)->where('sms_type', 'non-masking')->count();

        // Get last 5 transactions
        $last_transactions = Transaction::where('company_id', $company->id)
            ->orderBy('date', 'desc')
            ->take(5)
            ->get();

        $balance_msgs = $balance / 0.5;

        // Get last month's name dynamically using Carbon
        $last_month_name = Carbon::now()->subMonth()->format('F');

        // Pass data to the view
        return view('dashboard', compact(
            'sms_last_week',
            'cost_last_week',
            'sms_in_last_month',
            'cost_in_last_month',
            'balance',
            'balance_msgs',
            'masking_sms_quantity',
            'non_masking_sms_quantity',
            'last_transactions',
            'last_month_name'
        ));
    }



    public function switch()
    {
        return redirect()->route('home');
    }
}
