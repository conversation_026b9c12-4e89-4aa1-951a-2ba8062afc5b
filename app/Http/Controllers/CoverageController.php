<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreCoverageRequest;
use App\Http\Requests\UpdateCoverageRequest;
use App\Http\Services\CoverageService;
use App\Models\Coverage;
use App\Models\Server;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CoverageController extends Controller
{
    private $coverage;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function __construct(CoverageService $coverage)
    {
        $this->coverage = $coverage;
        $this->middleware(['permission:coverage-list|coverage-create|coverage-edit|coverage-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:coverage-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:coverage-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:coverage-delete'], ['only' => ['destroy']]);
    }

    /**
     * Browse All user using Data Table
     *
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws \Yajra\DataTables\Exceptions\Exception
     */
    public function browse(Request $request)
    {
        return datatables()->of(
            Coverage::with('company:id,name')
        )
            ->addColumn('company_name', function (Coverage $coverage) {
                return $coverage->company->name;

            })
            ->addColumn('action', function (Coverage $coverage) {
                $action = '';
                if (Auth::user()->hasRole('super-admin')) {
                    $action = '<button href="#" class="btn btn-danger btn-sm delete-button" data-endpoint="'.route('coverages.destroy', $coverage->id).'" >Delete</button>';
                    $action .= ' <a href="'.route('coverages.edit', $coverage->id).'" class="btn btn-secondary btn-sm" >Edit</a>';
                }

                return $action;
            })
            ->rawColumns(['action', 'company_name'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('coverages.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $operators = Common::operators();
        $prefixes = Common::prefixes();
        $servers = Server::where('status', 'enabled')->get();

        return view('coverages.create', compact('operators', 'servers', 'prefixes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCoverageRequest $request)
    {
        $this->coverage->store($request);

        return redirect()->route('coverages.index')
            ->with('success', 'Coverage has been added successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Coverage $coverage)
    {
        $operators = Common::operators();
        $prefixes = Common::prefixes();
        $servers = Server::where('status', 'enabled')->get();

        return view('coverages.edit', compact('coverage', 'operators', 'servers', 'prefixes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCoverageRequest $request, Coverage $coverage)
    {
        $this->coverage->update($request, $coverage);

        return redirect()->route('coverages.index')
            ->with('success', 'Coverage has been updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Coverage $coverage)
    {
        $this->coverage->delete($coverage);

        return response([
            'success' => true,
            'message' => 'Coverage has been deleted successfully.',
        ]);
    }
}
