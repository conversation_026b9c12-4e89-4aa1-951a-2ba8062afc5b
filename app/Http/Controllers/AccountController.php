<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\Gateway;
use App\Tenant\Manager;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Rappasoft\LaravelAuthenticationLog\Models\AuthenticationLog;

class AccountController extends Controller
{
    public function rechargeHistory()
    {
        $company = app(Manager::class)->getTenant();

        return view('account.recharge-history', compact('company'));
    }

    public function addFunds()
    {
        $company = app(Manager::class)->getTenant();
        $gateways = Gateway::where('status', '=', 'enabled')->get();

        return view('account.add-funds', compact('gateways', 'company'));
    }

    public function apiKeys()
    {
        $company = app(Manager::class)->getTenant();

        return view('account.api-keys', compact('company'));
    }

    public function logs()
    {
        $company = app(Manager::class)->getTenant();

        $user = Auth::user();
        $logs = AuthenticationLog::where('authenticatable_id', '=', $user->id)->orderBy('login_at', 'DESC')->get();

        return view('account.logs', compact('logs', 'company'));
    }

    public function overview()
    {
        $company = app(Manager::class)->getTenant();
        $user = Auth::user();

        return view('account.overview', compact('company', 'user'));
    }

    public function settings(Request $request)
    {
        $company = app(Manager::class)->getTenant();
        $user = auth()->user();

        return view('account.settings', compact('company', 'user'));
    }

    public function settingsUpdate(Request $request)
    {
        DB::beginTransaction();

        try {
            $company = app(Manager::class)->getTenant();
            $company->name = $request->company;
            $company->save();

            $user = auth()->user();
            $user->name = $request->name;
            $user->phone = $request->phone;

            if ($request->hasFile('avatar')) {
                $user->photo = $this->uploadFile($request->file('avatar'));
            }

            $user->save();
            DB::commit();

            return redirect()->route('account.settings')
                ->with('success', __('Account settings have been updated.'));
        } catch (Exception $e) {
            DB::rollback();

            return redirect()->back()->with('error', __('Something went wrong, please try again.'));
        }
    }

    public function updateEmail(Request $request)
    {
        $request->validate([
            'current_email' => 'required|email',
            'new_email' => 'required|email|unique:users,email,' . auth()->id(),
            'confirm_email' => 'required|email|same:new_email',
        ]);

        $user = auth()->user();

        // Verify current email matches
        if ($request->current_email !== $user->email) {
            return response()->json([
                'success' => false,
                'message' => 'Current email does not match our records.'
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user->email = $request->new_email;
            $user->email_verified_at = null; // Reset email verification
            $user->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Email updated successfully. Please verify your new email address.',
                'new_email' => $request->new_email
            ]);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong, please try again.'
            ], 500);
        }
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => ['required', 'string', Password::defaults()],
            'confirm_password' => 'required|string|same:new_password',
        ]);

        $user = auth()->user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect.'
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user->password = Hash::make($request->new_password);
            $user->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Password updated successfully.'
            ]);
        } catch (Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong, please try again.'
            ], 500);
        }
    }

    protected function uploadFile($file, $destinationPath = 'uploads/avatars')
    {
        $destination = public_path($destinationPath);
        if (! file_exists($destination)) {
            mkdir($destination, 0755, true);
        }
        $filename = time().'_'.uniqid().'.'.$file->getClientOriginalExtension();
        $file->move($destination, $filename);

        return $destinationPath.'/'.$filename;
    }

    public function recharge()
    {
        $company = app(Manager::class)->getTenant();

        return view('account.recharge', compact('company'));
    }

    public function domains()
    {
        $company = app(Manager::class)->getTenant();
        $domains = $company->domains()->orderBy('created_at', 'desc')->get();

        return view('account.domains', compact('company', 'domains'));
    }

    public function storeDomain(Request $request)
    {
        $request->validate([
            'domain' => 'required|string|max:255|unique:domains,domain',
        ]);

        try {
            $company = app(Manager::class)->getTenant();

            Domain::create([
                'domain' => $request->domain,
                'company_id' => $company->id,
            ]);

            return redirect()->route('account.domains')
                ->with('success', __('Domain has been added successfully.'));
        } catch (Exception $e) {
            return redirect()->back()
                ->with('error', __('Something went wrong, please try again.'))
                ->withInput();
        }
    }

    public function destroyDomain($domainId)
    {
        try {
            $company = app(Manager::class)->getTenant();

            if (!$company) {
                return redirect()->route('account.domains')
                    ->with('error', __('No tenant context found.'));
            }

            // Find the domain manually with tenant scope
            $domain = Domain::where('id', $domainId)
                           ->where('company_id', $company->id)
                           ->first();

            if (!$domain) {
                return redirect()->route('account.domains')
                    ->with('error', __('Domain not found or unauthorized action.'));
            }

            $domain->delete();

            return redirect()->route('account.domains')
                ->with('success', __('Domain has been deleted successfully.'));
        } catch (Exception $e) {
            return redirect()->back()
                ->with('error', __('Something went wrong, please try again.'));
        }
    }

    public function verifyDomain($domainId)
    {
        $company = app(Manager::class)->getTenant();

        if (!$company) {
            return response()->json([
                'success' => false,
                'message' => 'No tenant context found.'
            ], 403);
        }

        // Find the domain manually with tenant scope
        $domain = Domain::where('id', $domainId)
                       ->where('company_id', $company->id)
                       ->first();

        if (!$domain) {
            return response()->json([
                'success' => false,
                'message' => 'Domain not found or you are not authorized to verify this domain.'
            ], 404);
        }

        // Check cache to prevent too frequent verification attempts
        $cacheKey = "domain_verification_{$domain->id}";
        $lastAttemptTime = Cache::get($cacheKey);

        if ($lastAttemptTime) {
            $cacheDuration = config('dns.verification.cache_duration', 300);
            $nextAttemptTime = $lastAttemptTime->addSeconds($cacheDuration);

            if (now()->isBefore($nextAttemptTime)) {
                $remainingSeconds = now()->diffInSeconds($nextAttemptTime);
                $remainingMinutes = ceil($remainingSeconds / 60);
                $nextAttemptTimeFormatted = $nextAttemptTime->format('H:i:s');

                return response()->json([
                    'success' => false,
                    'message' => "Please wait {$remainingMinutes} minute(s) before attempting verification again. You can try again after {$nextAttemptTimeFormatted}."
                ]);
            }
        }

        try {
            $isVerified = $this->performDomainVerification($domain);

            if ($isVerified) {
                $domain->update([
                    'dns_status' => 'active',
                    'last_verified_at' => now(),
                    'verification_notes' => 'Domain successfully verified and active.',
                    'verification_attempts' => $domain->verification_attempts + 1,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Domain verified successfully! Your domain is now active.'
                ]);
            } else {
                $domain->update([
                    'dns_status' => 'failed',
                    'last_verified_at' => now(),
                    'verification_notes' => 'Domain verification failed. Please check your DNS settings.',
                    'verification_attempts' => $domain->verification_attempts + 1,
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Domain verification failed. Please check your DNS settings and try again.'
                ]);
            }
        } catch (Exception $e) {
            $domain->update([
                'dns_status' => 'failed',
                'last_verified_at' => now(),
                'verification_notes' => 'Verification error: ' . $e->getMessage(),
                'verification_attempts' => $domain->verification_attempts + 1,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during verification. Please try again later.'
            ]);
        } finally {
            // Cache the verification attempt to prevent spam
            Cache::put($cacheKey, now(), config('dns.verification.cache_duration', 300));
        }
    }

    private function performDomainVerification(Domain $domain): bool
    {
        $expectedIp = config('dns.server_ip');
        $timeout = config('dns.verification.timeout', 30);

        try {
            // Use gethostbyname to resolve domain to IP
            $resolvedIp = gethostbyname($domain->domain);

            // If gethostbyname fails, it returns the domain name itself
            if ($resolvedIp === $domain->domain) {
                return false;
            }

            // Check if resolved IP matches our server IP
            return $resolvedIp === $expectedIp;

        } catch (Exception $e) {
            // Log the error for debugging
            \Log::error("Domain verification failed for {$domain->domain}: " . $e->getMessage());
            return false;
        }
    }
}
