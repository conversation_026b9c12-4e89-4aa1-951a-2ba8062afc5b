<?php

namespace App\Http\Controllers;

use App\Helpers\Common;
use App\Http\Requests\StoreGatewayRequest;
use App\Http\Requests\UpdateGatewayRequest;
use App\Http\Services\GatewayService;
use App\Models\Gateway;
use Illuminate\Http\Request;

class GatewayController extends Controller
{
    private $gateway;

    public function __construct(GatewayService $gateway)
    {
        $this->gateway = $gateway;
        $this->middleware(['permission:gateway-list|gateway-create|gateway-edit|gateway-delete'], ['only' => ['index', 'show']]);
        $this->middleware(['permission:gateway-create'], ['only' => ['create', 'store']]);
        $this->middleware(['permission:gateway-edit'], ['only' => ['edit', 'update']]);
        $this->middleware(['permission:gateway-delete'], ['only' => ['destroy']]);
    }

    public function browse(Request $request): \Illuminate\Http\JsonResponse
    {
        return datatables()->of(
            Gateway::query()
        )
            ->editColumn('test_mode', function (Gateway $gateway) {
                return $gateway->test_mode == 'yes' ? 'Yes' : 'No';
            })
            ->addColumn('action', function (Gateway $gateway) {
                $action = ' <a href="'.route('admin.gateways.edit', $gateway->id).'" class="btn btn-secondary btn-sm" >Edit</a>';

                return $action;
            })
            ->rawColumns(['action'])
            ->toJson();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('admin.gateways.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.gateways.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreGatewayRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Gateway $gateways)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gateway $gateway)
    {
        $paymentProviders = Common::paymentProviders();

        return view('admin.gateways.edit', [
            'gateway' => $gateway,
            'paymentProviders' => $paymentProviders,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateGatewayRequest $request, Gateway $gateway): \Illuminate\Http\RedirectResponse
    {

        $this->gateway->update($request, $gateway);

        return redirect()->route('admin.gateways.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gateway $gateways)
    {
        //
    }
}
