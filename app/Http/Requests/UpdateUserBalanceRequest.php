<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserBalanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'recharge_amount' => 'required|numeric',
            'user_id' => 'required|integer|exists:users,id',
            'remarks' => 'required|string',
            'gateway_fee' => 'numeric',
            'payment_method' => 'required|string',
        ];
    }
}
