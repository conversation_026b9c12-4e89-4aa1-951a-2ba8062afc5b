<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'api_link' => 'required|url|max:500',
            'gateway_type' => 'required|string|exists:gateway_provider_types,name',
            'status' => 'required|in:enabled,disabled',

            // Authentication validation - either api_key OR username+password required
            'api_key' => 'nullable|string|max:500|required_without_all:username,password',
            'username' => 'nullable|string|max:255|required_with:password|required_without:api_key',
            'password' => 'nullable|string|max:255|required_with:username|required_without:api_key',

            // Optional fields
            'sending_limit' => 'nullable|integer|min:1',
            'time_base' => 'nullable|integer|min:1',
            'time_unit' => 'nullable|string|in:minute,hour,day',
            'api_parameters' => 'nullable|json',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'api_key.required_without_all' => 'Either API Key or Username & Password is required for authentication.',
            'username.required_without' => 'Username is required when API Key is not provided.',
            'username.required_with' => 'Username is required when Password is provided.',
            'password.required_without' => 'Password is required when API Key is not provided.',
            'password.required_with' => 'Password is required when Username is provided.',
            'gateway_type.exists' => 'The selected gateway provider type is invalid.',
        ];
    }
}
