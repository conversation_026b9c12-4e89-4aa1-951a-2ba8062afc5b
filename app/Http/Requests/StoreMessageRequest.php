<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMessageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'sender_id' => 'required|integer|exists:senders,id',
            'phone_numbers' => ['required', 'string', function ($attribute, $value, $fail) {
                $numbers = preg_split('/\r\n|\r|\n/', trim($value));
                foreach ($numbers as $number) {
                    if (! preg_match('/^\d{8,15}$/', trim($number))) {
                        $fail("The $attribute contains an invalid phone number: $number.");
                    }
                }
            }],
            'sms_type' => 'required|string|in:text,flash_unicode,flash,unicode',
            'sms_content' => 'required|string|max:1000',
            'is_scheduled' => 'required|boolean',
            'scheduled_at' => 'nullable|date|required_if:is_scheduled,true|after:now',
            '_token' => 'required|string',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_scheduled' => $this->is_scheduled == '1' ? true : false,
        ]);
    }
}
