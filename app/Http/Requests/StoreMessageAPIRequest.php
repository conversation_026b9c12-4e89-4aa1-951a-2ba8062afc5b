<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class StoreMessageAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required_without:api_key|string',
            'password' => 'required_without:api_key|string',
            'api_key' => 'required_without_all:username,password|string',
            'sender_id' => 'required|string|max:255',
            'phone_numbers' => 'required|string',
            'type' => ['required', Rule::in(['text', 'unicode'])],
            'message' => 'required|string',
            'scheduled_date_time' => 'nullable|date|after_or_equal:now',
        ];
    }

    public function messages()
    {
        return [
            'username.required_without' => 'The username field is required when API key is not present.',
            'password.required_without' => 'The password field is required when API key is not present.',
            'api_key.required_without_all' => 'The API key field is required when neither username nor password are present.',
            'sender_id.required' => 'The sender ID field is required.',
            'phone_numbers.required' => 'The phone numbers field is required.',
            'type.required' => 'The type field is required.',
            'type.in' => 'The type must be either text or unicode.',
            'message.required' => 'The message field is required.',
            'scheduled_date_time.date' => 'The scheduled date time must be a valid date.',
            'scheduled_date_time.after_or_equal' => 'The scheduled date time must be a date after or equal to now.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors();
        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => 'Validation errors',
            'errors' => $errors,
        ], 422));
    }
}
