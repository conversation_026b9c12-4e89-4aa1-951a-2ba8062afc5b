<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSenderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'file|mimes:jpg,jpeg,png,pdf|max:2048', // File types and size limit
        ];

        // Additional rules for super-admins only
        if ($this->user()->hasRole('super-admin')) {
            $rules['is_default'] = 'required|boolean';
            $rules['status'] = [
                'required',
                Rule::in(['Pending', 'Pending Approved', 'Approved', 'Disabled']), // Example statuses
            ];
            $rules['is_server_default'] = 'nullable|boolean'; // Boolean for server default flag
        }

        return $rules;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'is_default' => $this->has('is_default') ? (bool) $this->is_default : 0,
            'is_server_default' => $this->has('is_server_default') ? (bool) $this->is_server_default : 0,
        ]);
    }
}
