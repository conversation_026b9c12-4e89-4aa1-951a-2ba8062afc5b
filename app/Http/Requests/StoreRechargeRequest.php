<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRechargeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'payment_method' => 'required|string|in:Manual,Ssl,BkashPay',
            'recharge_amount' => 'required|numeric',
            'gateway_fee' => 'required|numeric|min:0',
            'final_amount' => 'required|numeric',
            'remarks' => 'nullable|string',
        ];
    }
}
