<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSenderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|unique:senders,name|string',
            'server_id' => 'nullable|exists:servers,id', // Ensure the server exists
            'is_server_default' => 'nullable|boolean',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png|max:2048', // Accept specific file types and max size 2MB
        ];
    }
}
