<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\ServerRepository;
use App\Models\Server;
use App\Models\GatewayProviderType;
use Illuminate\Http\Request;

class ServerService
{
    private $server;

    public function __construct(?ServerRepository $repo = null)
    {
        $this->server = $repo ?: new ServerRepository;
    }

    public function store(Request $request)
    {
        // Get base server data
        $serverData = array_merge(
            $request->only([
                'name', 'api_link', 'api_key', 'username', 'password', 'status',
                'sending_limit', 'time_base', 'time_unit', 'gateway_type'
            ]),
            ['user_id' => auth()->id()]
        );

        // Handle dynamic gateway configuration
        if ($request->has('gateway_type') && $request->gateway_type !== 'custom') {
            $providerType = GatewayProviderType::where('name', $request->gateway_type)
                ->where('is_active', true)
                ->first();

            if ($providerType) {
                $serverData = array_merge($serverData, [
                    'auth_method' => $providerType->auth_method,
                    'http_method' => $providerType->http_method,
                    'api_parameters' => $providerType->api_parameters_template,
                    'headers' => $providerType->headers_template,
                    'success_response_pattern' => $providerType->success_response_pattern,
                    'error_response_pattern' => $providerType->error_response_pattern,
                    'supported_message_types' => $providerType->supported_message_types,
                    'is_legacy' => $providerType->is_built_in && in_array($providerType->name, ['route_mobile', 'elit_buzz'])
                ]);
            }
        } else {
            // Handle custom gateway configuration
            $serverData = array_merge($serverData, [
                'auth_method' => $request->input('auth_method', 'api_key'),
                'http_method' => $request->input('http_method', 'POST'),
                'api_parameters' => $request->input('api_parameters', []),
                'headers' => $request->input('headers', []),
                'success_response_pattern' => $request->input('success_response_pattern'),
                'error_response_pattern' => $request->input('error_response_pattern'),
                'supported_message_types' => $request->input('supported_message_types', ['text']),
                'is_legacy' => false
            ]);
        }

        try {
            $this->server->store($serverData);
        } catch (\Throwable $e) {
            throw new ErrorMessageException('Servers Create Error: '.$e->getMessage());
        }
    }

    public function update(Request $request, Server $server)
    {
        // Get base server data
        $serverData = $request->only([
            'name', 'api_link', 'api_key', 'username', 'password', 'status',
            'sending_limit', 'time_base', 'time_unit', 'gateway_type'
        ]);

        // Handle dynamic gateway configuration
        if ($request->has('gateway_type') && $request->gateway_type !== 'custom') {
            $providerType = GatewayProviderType::where('name', $request->gateway_type)
                ->where('is_active', true)
                ->first();

            if ($providerType) {
                $serverData = array_merge($serverData, [
                    'auth_method' => $providerType->auth_method,
                    'http_method' => $providerType->http_method,
                    'api_parameters' => $providerType->api_parameters_template,
                    'headers' => $providerType->headers_template,
                    'success_response_pattern' => $providerType->success_response_pattern,
                    'error_response_pattern' => $providerType->error_response_pattern,
                    'supported_message_types' => $providerType->supported_message_types,
                    'is_legacy' => $providerType->is_built_in && in_array($providerType->name, ['route_mobile', 'elit_buzz'])
                ]);
            }
        } else {
            // Handle custom gateway configuration
            $serverData = array_merge($serverData, [
                'auth_method' => $request->input('auth_method', 'api_key'),
                'http_method' => $request->input('http_method', 'POST'),
                'api_parameters' => $request->input('api_parameters', []),
                'headers' => $request->input('headers', []),
                'success_response_pattern' => $request->input('success_response_pattern'),
                'error_response_pattern' => $request->input('error_response_pattern'),
                'supported_message_types' => $request->input('supported_message_types', ['text']),
                'is_legacy' => false
            ]);
        }

        try {
            $this->server->setContext($server)->update(array_filter($serverData));
        } catch (\Exception $e) {
            throw new ErrorMessageException('Server update Error: '.$e->getMessage());
        }
    }

    public function delete(Server $servers)
    {
        $this->server->setContext($servers)->delete();
    }
}
