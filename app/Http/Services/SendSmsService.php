<?php

namespace App\Http\Services;

use App\Exceptions\SmsSendingResponseException;
use App\Helpers\SmsResponse;
use App\Http\Repositories\CompanyRepository;
use App\Http\Repositories\SenderRepository;
use App\Tenant\Manager;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SendSmsService
{
    private $companyRepo;

    private $senderRepo;

    private $message;

    public function __construct(?CompanyRepository $repo = null)
    {
        $this->companyRepo = $repo ?: new CompanyRepository;
        $this->message = new MessageService;
        $this->senderRepo = new SenderRepository;
    }

    /**
     * Find company to send Sms
     *
     * @return \App\Models\Company|null
     */
    public function findCompany(Request $request)
    {
        $company = $this->companyRepo->findByApiKey($request->api_key);

        if ($request->has('api_key') && ! empty($company)) {
            return $company;
        }

        $credentials = [
            'username' => $request->username,
            'password' => $request->password,
        ];

        if (! Auth::attempt($credentials)) {
            return null;
        }

        return auth()->user()->companies->first();
    }

    /**
     * Send Sms
     */
    public function smsSend(Request $request)
    {
        $scheduledAt = null;
        $company = app()->make(Manager::class)->getTenant();

        if (empty($company)) {
            throw new SmsSendingResponseException(1003);
        }

        try {
            if (! empty($request->scheduled_date_time)) {
                $scheduledAt = Carbon::parse($request->scheduled_date_time);
            }
        } catch (\Throwable $th) {
            return response(SmsResponse::error(1016));
        }

        $user = $company->users->first();
        $sender = $this->senderRepo->getSenderByNameAndCompany($request->sender_id, $company);

        if (empty($sender) || is_null($sender)) {
            return response(SmsResponse::error(1002));
        }

        // if $sender->is_masking is true then  server_id will be $sender->server_id
        // if $sender->is_masking is false then need change $sender_id by bellow query
        // $defaultSender = Sender::where('is_server_default', 1)->where('server_id', $coverage['server_id'])->first()
        // sender_id = $defaultSender->id;

        $request->merge([
            'sms_type' => $request->type,
            'sms_content' => $request->message,
            'user_id' => $user->id,
            'sender_id' => $sender->id,
            'is_scheduled' => empty($scheduledAt) ? 0 : 1,
            'schedule_at' => $scheduledAt,
        ]);

        $store = $this->message->store($request);

        // Handle failure if no messages were stored
        if (isset($store['status']) && $store['status'] === 'failed') {
            return response(SmsResponse::error(1001, $store['message']));
        }

        if (! empty($scheduledAt)) {
            return response(SmsResponse::successScheduled());
        }

        return response(SmsResponse::success());
    }
}
