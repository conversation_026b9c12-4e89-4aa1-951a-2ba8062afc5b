<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\PaymentRepository;
use App\Http\Repositories\RechargeRepository;
use App\Http\Repositories\UserRepository;
use App\Http\Services\PaymentGateways\GatewayBindingInterface;
use App\Tenant\Manager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentService
{
    private $payment;

    private $gateway;

    private $recharge;

    private $user;

    private $userRepo;

    public function __construct(?PaymentRepository $repo = null)
    {
        $this->payment = $repo ?: new PaymentRepository;
        $this->gateway = app()->make(GatewayBindingInterface::class);
        $this->recharge = new RechargeRepository;
        $this->userRepo = new UserRepository;
    }

    /**
     * Proceed to payment
     */
    public function proceedToPayment(Request $request)
    {
        $company = $this->findCompany($request);

        $payment = $this->payment->setUser($this->user)->setCompany($company)->initiatePayment($request);

        if (!empty($request->get_payment_info)) {
            return $payment;
        }

        return $this->gateway->pay($payment, $request);
    }

    public function getUser()
    {
        return auth()->user();
    }

    public function findCompany(Request $request)
    {
        if ($request->user_id && $user = $this->userRepo->get($request->user_id)) {
            $this->user = $user;

            return $this->user->companies->first();
        }

        $this->user = $this->getUser();

        return app(Manager::class)->getTenant();
    }

    public function success(Request $request)
    {
        DB::beginTransaction();

        try {
            $this->gateway->success();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw new ErrorMessageException('Error : ' . $e->getMessage());
        }
    }

    public function ipn(Request $request)
    {
        if (!$request->input('tran_id')) {
            return;
        }

        DB::beginTransaction();

        try {
            $resp = $this->gateway->ipn();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw new ErrorMessageException('Error : ' . $e->getMessage());
        }

        if ($resp) {
            echo 'payment complete';
        }
    }

    public function fail(Request $request)
    {
        DB::beginTransaction();

        try {
            $this->gateway->fail();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw new ErrorMessageException($e->getMessage());
        }
    }

    public function canceled(Request $request)
    {
        DB::beginTransaction();

        try {
            $this->gateway->canceled();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw new ErrorMessageException($e->getMessage());
        }
    }
}
