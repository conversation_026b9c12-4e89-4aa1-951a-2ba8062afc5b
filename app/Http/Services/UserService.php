<?php

namespace App\Http\Services;

use App\Events\UserRegistered;
use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\UserRepository;
use App\Models\User;
use App\Notifications\AutoGeneratedPassword;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserService
{
    /**
     * @var UserRepository
     */
    private $users;

    public function __construct(?UserRepository $repo = null)
    {
        $this->users = $repo ?: new UserRepository;
    }

    public function store(Request $request)
    {
        $username = $this->generateUniqueUsername();
        $password = Str::random(12);
        $userData = $request->only('name', 'email', 'phone', 'role_id', 'minimum_recharge_amount');

        $userData = array_merge($userData, [
            'username' => $username,
            'password' => Hash::make($password),
            'parent_id' => auth()->user()->id,
            'created_by' => auth()->user()->id,
            'current_balance' => $request->balance,
            'balance_expired' => Carbon::now()->addMonths($request->validity)->toDateTimeString(),
            'last_activity' => now()->getTimestamp(),
            'email_verified_at' => now(),
        ]);

        try {
            $user = $this->users->store($userData);
            event(new UserRegistered($user));
            $user->notify(new AutoGeneratedPassword($username, $password));
        } catch (\Exception $e) {
            throw new ErrorMessageException('User Create Error: '.$e->getMessage());
        }
    }

    public function delete($data)
    {
        $this->users->setContext($data)->delete();
    }

    /**
     * @return string
     */
    private function generateUniqueUsername()
    {
        // Generate a random username
        $username = 'C'.str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);

        // Check if the generated username already exists in the database
        while (User::where('username', $username)->exists()) {
            // If it exists, generate a new one
            $username = 'C'.str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        }

        return $username;
    }
}
