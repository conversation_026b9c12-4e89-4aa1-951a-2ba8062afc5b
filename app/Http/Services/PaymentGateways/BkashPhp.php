<?php

namespace App\Http\Services\PaymentGateways;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class BkashPhp
{
    private $baseUrl;

    private $appKey;

    private $appSecret;

    private $username;

    private $password;

    public function __construct(array $credential)
    {
        $this->baseUrl = $credential['test_mode'] == 'yes'
            ? 'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout'
            : 'https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout';
        $this->appKey = $credential['app_key'];
        $this->appSecret = $credential['app_secret'];
        $this->username = $credential['username'];
        $this->password = $credential['password'];
    }

    public function getToken(): string
    {
        $cacheKey = 'bkash_token';
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        $response = Http::withHeaders([
            'username' => $this->username,
            'password' => $this->password,
            'Content-Type' => 'application/json',
        ])->post($this->baseUrl.'/token/grant', [
            'app_key' => $this->appKey,
            'app_secret' => $this->appSecret,
        ]);
        $data = $response->json();
        if ($response->successful() && isset($data['id_token'])) {
            $token = $data['id_token'];
            $expiresIn = $data['expires_in'] ?? 300;
            Cache::put($cacheKey, $token, now()->addSeconds($expiresIn));
            if (isset($data['refresh_token'])) {
                Cache::put('bkash_refresh_token', $data['refresh_token'], now()->addDays(30));
            }

            return $token;
        }
        throw new \Exception($data['statusMessage'] ?? 'Failed to generate token');
    }

    public function createPayment(array $data)
    {
        $token = $this->getToken();

        $url = $this->baseUrl.'/create';

        $user = Auth::user();

        $body = [
            'mode' => '0011',  // indicating Checkout (URL) mode
            'payerReference' => $user->phone ?? Str::random(10),  // the reference value, such as wallet number
            'callbackURL' => $data['success_url'],  // the base URL for success, failure, and canceled transaction callbacks
            'amount' => (string) $data['amount'],  // amount to be paid
            'currency' => 'BDT',  // currency for the payment
            'intent' => 'sale',  // intent of the payment (e.g., "sale" for a typical payment)
            'merchantInvoiceNumber' => $data['invoice_number'],  // unique invoice number
        ];

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'authorization' => $token,  // Token generated using the Grant or Refresh Token API
            'X-App-Key' => $this->appKey,  // Application Key provided during onboarding
            'X-Amz-Date' => gmdate('Ymd\THis\Z'),
        ];

        try {
            $response = Http::withHeaders($headers)->post($url, $body);

            // Check if the response is successful before decoding the JSON
            if ($response->successful()) {
                // Decode the response body as JSON
                $responseData = $response->json();

                // Check if 'bkashURL' is set in the response
                if (isset($responseData['bkashURL'])) {
                    return $responseData['bkashURL'];  // Return the URL for the user to complete the payment
                }

                // If no 'bkashURL' in the response, throw an exception
                throw new \Exception($responseData['statusMessage'] ?? 'Failed to create payment');
            } else {
                // Handle the case when the request is not successful
                throw new \Exception('Failed to create payment, response status: '.$response->status());
            }
        } catch (\Exception $e) {
            throw new \Exception('Error during payment creation: '.$e->getMessage());
        }
    }

    public function verifyPayment(string $paymentId): array
    {
        $token = $this->getToken();
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'authorization' => $token,
            'x-app-key' => $this->appKey,
        ])->post($this->baseUrl.'/execute', [
            'paymentID' => $paymentId,
        ]);

        if ($response->successful()) {
            $responseData = $response->json();

            if (isset($responseData['trxID'])) {
                return $responseData;
            }
        }

        throw new \Exception($data['statusMessage'] ?? 'Failed to execute payment');
    }

    public function queryPayment(string $paymentId): array
    {
        $token = $this->getToken();
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'authorization' => $token,
            'x-app-key' => $this->appKey,
        ])->post($this->baseUrl.'/payment/status', [
            'paymentID' => $paymentId,
        ]);
        $data = $response->json();
        if ($response->successful() && isset($data['paymentID'])) {
            return $data;
        }
        throw new \Exception($data['statusMessage'] ?? 'Failed to query payment');
    }

    public function refundPayment(array $data): array
    {
        $token = $this->getToken();
        $payload = [
            'paymentID' => $data['payment_id'],
            'trxID' => $data['trx_id'],
            'amount' => (string) $data['amount'],
            'sku' => $data['sku'] ?? null,
            'reason' => $data['reason'] ?? 'Refund requested by customer',
        ];
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'authorization' => $token,
            'x-app-key' => $this->appKey,
        ])->post($this->baseUrl.'/payment/refund', $payload);
        $responseData = $response->json();
        if ($response->successful() && isset($responseData['refundTrxID'])) {
            return $responseData;
        }
        throw new \Exception($responseData['statusMessage'] ?? 'Failed to refund payment');
    }
}
