<?php

namespace App\Http\Services\PaymentGateways\Gateways;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\PaymentRepository;
use App\Http\Services\PaymentGateways\BkashPhp;
use App\Http\Services\PaymentGateways\GatewayBindingInterface;
use App\Models\Gateway;
use App\Models\Payment;
use Illuminate\Http\Request;

class BkashPay implements GatewayBindingInterface
{
    private $request;

    private $paymentRepo;

    private $bkash;

    public function __construct(Request $request, Gateway $gateway)
    {
        $this->request = $request;
        $this->paymentRepo = new PaymentRepository;
        $credential = [
            'username' => $gateway->username,
            'password' => $gateway->password,
            'app_key' => $gateway->app_key,
            'app_secret' => $gateway->app_secret,
            'test_mode' => $gateway->test_mode,
        ];
        $this->bkash = new BkashPhp($credential);
    }

    public function pay(Payment $payment)
    {
        $requestData = [
            'amount' => $payment->amount,
            'success_url' => config('app.url').'/payment/success',
            'brand_name' => 'SMS',
            'invoice_number' => $payment->transaction_id,
        ];
        try {
            $bKashURL = $this->bkash->createPayment($requestData);
            if (! empty($bKashURL)) {
                return redirect()->away($bKashURL);
            }
        } catch (\Exception $e) {
            throw new ErrorMessageException($e->getMessage());
        }
    }

    public function success()
    {
        $paymentId = $this->request->input('paymentID');

        try {
            $response = $this->bkash->verifyPayment($paymentId);
            $transId = $response['merchantInvoiceNumber'];

            // Check order status in order table against the transaction id or order id.
            $payment = $this->paymentRepo->getPaymentByTransId($transId);

            if(empty($payment)){
                throw new ErrorMessageException('Invalid payment information');
            }

            if ($payment->payment_status == 'complete') {
                return true;
            }

            if ($payment->payment_status != 'pending') {
                throw new ErrorMessageException('Invalid payment information');
            }

            $this->paymentRepo->setContext($payment)->successPayment($this->request->all());
            return true;

        } catch (\Exception $e) {
            throw new ErrorMessageException($e->getMessage());
        }
    }

    public function ipn()
    {
        return $this->success();
    }
}
