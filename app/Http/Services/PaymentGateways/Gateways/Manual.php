<?php

namespace App\Http\Services\PaymentGateways\Gateways;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\PaymentRepository;
use App\Http\Services\PaymentGateways\GatewayBindingInterface;
use App\Models\Gateway;
use App\Models\Payment;
use Illuminate\Http\Request;

class Manual implements GatewayBindingInterface
{
    /**
     * Http request
     *
     * @var \Illuminate\Http\Request
     */
    private $request;

    /**
     * Payment Repository
     *
     * @var \App\Http\Repositories\PaymentRepository
     */
    private $paymentRepo;

    /**
     * SSl Payment Constructor
     *
     * @param \Illuminate\Http\Request
     */
    public function __construct(Request $request, Gateway $gateway)
    {
        $this->request = $request;
        $this->paymentRepo = new PaymentRepository;
    }

    /**
     * Proceed to payment
     *
     * @return mixed|void
     */
    public function pay(Payment $payment)
    {
        return redirect()->route('account.recharge-history');
    }

    /**
     * Proceed to success
     *
     * @return mixed
     */
    public function success()
    {
        //Check order status in order table against the transaction id or order id.
        $payment = $this->paymentRepo->getPaymentByTransId($this->request->trans_id);

        if ($payment->payment_status == 'complete') {
            return true;
        }

        if ($payment->payment_status != 'pending') {
            throw new ErrorMessageException('Invalid payment information');
        }

        $this->paymentRepo->setContext($payment)->successPayment($this->request->all());

        return true;
    }

    /**
     * Proceed to success
     *
     * @return mixed
     */
    public function ipn()
    {
        return $this->success();
    }
}
