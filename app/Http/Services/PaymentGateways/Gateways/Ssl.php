<?php

namespace App\Http\Services\PaymentGateways\Gateways;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\PaymentRepository;
use App\Http\Services\PaymentGateways\GatewayBindingInterface;
use App\Http\Services\PaymentGateways\GatewayErrorBindingInterface;
use App\Library\SslCommerce\SslCommerceNotification;
use App\Models\Gateway;
use App\Models\Payment;
use Illuminate\Http\Request;

class Ssl implements GatewayBindingInterface, GatewayErrorBindingInterface
{
    /**
     * Http request
     *
     * @var Request
     */
    private $request;

    /**
     * Payment Repository
     *
     * @var PaymentRepository
     */
    private $paymentRepo;

    /**
     * @var SslCommerceNotification
     */
    private $ssl;

    /**
     * SSl Payment Constructor
     *
     * @param Request
     */
    public function __construct(Request $request, Gateway $gateway)
    {
        $this->request = $request;
        $this->paymentRepo = new PaymentRepository;

        $this->ssl = new SslCommerceNotification;
        $this->ssl->setCredentials($gateway->username, $gateway->password);
    }

    /**
     * Proceed to payment
     *
     * @return mixed|void
     */
    public function pay(Payment $payment)
    {
        $user = auth()->user();
        $data = $this->ssl->fillData();
        $data['tran_id'] = $payment->transaction_id;
        $data['cus_name'] = $user->name;
        $data['cus_email'] = $user->email;
        $data['cus_phone'] = $user->phone;
        $data['total_amount'] = $this->request->final_amount;

        $paymentOptions = $this->ssl->makePayment($data, 'hosted');

        if (! is_array($paymentOptions)) {
            print_r($paymentOptions);
            $paymentOptions = [];
        }
    }

    /**
     * Proceed to success
     *
     * @return mixed
     */
    public function success()
    {

        $transId = $this->request->input('tran_id');
        $amount = $this->request->input('amount');
        $currency = $this->request->input('currency');

        // Check order status in order table against the transaction id or order id.
        $payment = $this->paymentRepo->getPaymentByTransId($transId);

        if ($payment->payment_status == 'complete') {
            return true;
        }

        if ($payment->payment_status != 'pending') {
            throw new ErrorMessageException('Invalid payment information');
        }

        $validation = $this->ssl->orderValidate($this->request->all(), $transId, $amount, $currency);

        if ($validation) {
            $this->paymentRepo->setContext($payment)->successPayment($this->request->all());

            return true;
        }

        return false;
    }

    /**
     * Proceed to success
     *
     * @return mixed
     */
    public function ipn()
    {
        return $this->success();
    }

    /**
     * Fail payment
     *
     * @return bool
     */
    public function fail()
    {
        $transId = $this->request->input('tran_id');

        $payment = $this->paymentRepo->getPaymentByTransId($transId);

        if (empty($payment)) {
            return false;
        }

        if ($payment->payment_status == 'complete') {
            return true;
        }

        $this->paymentRepo->setContext($payment)->failedPayment();

        return true;
    }

    /**
     * Canceled payment
     *
     * @return bool
     */
    public function canceled()
    {
        $transId = $this->request->input('tran_id');

        $payment = $this->paymentRepo->getPaymentByTransId($transId);

        if (empty($payment)) {
            return false;
        }

        if ($payment->payment_status == 'complete') {
            return true;
        }

        $this->paymentRepo->setContext($payment)->canceledPayment();

        return true;
    }
}
