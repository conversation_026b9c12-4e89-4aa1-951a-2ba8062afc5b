<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\SenderRepository;
use App\Models\Sender;
use Carbon\Carbon;
use Illuminate\Http\Request;
use PHPUnit\Event\Code\Throwable;

class SenderService
{
    private $sender;

    public function __construct(?SenderRepository $repo = null)
    {
        $this->sender = $repo ?: new SenderRepository;
    }

    /**
     * Store Sender
     *
     * @throws ErrorMessageException
     */
    public function store(Request $request)
    {
        try {
            $senderData = $request->only(['name', 'is_default', 'status']);
            $senderData['is_masking'] = is_numeric($request->name) ? 0 : 1;
            $this->sender->store($senderData);
        } catch (\Exception $e) {
            throw new ErrorMessageException('Sender Create Error: '.$e->getMessage());
        }
    }

    /**
     * Delete Sender
     */
    public function delete($sender)
    {
        $this->sender->setContext($sender)->delete();
    }

    /**
     * Update Sender
     *
     * @throws ErrorMessageException
     */
    public function update(Request $request, Sender $sender)
    {
        try {
            $senderData = $request->only(['name', 'is_default', 'status']);
            $senderData['is_masking'] = is_numeric($request->name) ? 0 : 1;
            $senderData['executed_at'] = Carbon::now();

            return $this->sender->setContext($sender)->update(array_filter($senderData));
        } catch (Throwable $e) {
            throw new ErrorMessageException('Sender Update Error: '.$e->getMessage());
        }
    }

    public function makeDefault(Sender $sender)
    {
        try {
            Sender::where('company_id', '=', $sender->company_id)->update([
                'is_default' => 0,
            ]);

            $this->sender->setContext($sender)->update([
                'is_default' => 1,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sender updated successfully',
            ]);

        } catch (Throwable $e) {
            return response([
                'success' => false,
                'message' => 'Sender Update Error:'.$e->getMessage(),
            ], 403);
        }
    }
}
