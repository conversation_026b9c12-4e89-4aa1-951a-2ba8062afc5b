<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\GatewayRepository;
use App\Models\Gateway;
use App\Models\Server;
use Illuminate\Http\Request;

class GatewayService
{
    private $gateway;

    public function __construct(?GatewayRepository $repo = null)
    {
        $this->gateway = $repo ?: new GatewayRepository;
    }

    public function store(Request $request)
    {

        $gatewayData = $request->only([
            'user_id',
            'company_id',
            'name',
            'class_name',
            'logo',
            'msisdn',
            'username',
            'password',
            'gateway_fee',
            'payment_mode',
            'status',
        ]);

        try {
            $this->gateway->store($gatewayData);
        } catch (\Exception $e) {
            throw new ErrorMessageException('Servers Create Error: '.$e->getMessage());
        }
    }

    public function update(Request $request, Gateway $gateway)
    {
        $gatewayData = $request->only([
            'user_id',
            'company_id',
            'name',
            'class_name',
            'logo',
            'msisdn',
            'username',
            'password',
            'gateway_fee',
            'test_mode',
            'status',
        ]);

        try {
            $this->gateway->setContext($gateway)->update(array_filter($gatewayData));
        } catch (\Exception $e) {
            throw new ErrorMessageException('Gateway Update Error: '.$e->getMessage());
        }
    }

    public function delete(Server $gateways)
    {
        $this->gateway->setContext($gateways)->delete();
    }
}
