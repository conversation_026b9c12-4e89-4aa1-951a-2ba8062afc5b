<?php

namespace App\Http\Services\SmsServers\Servers;

use App\Http\Services\SmsServers\SmsSendingBase;
use App\Http\Services\SmsServers\SmsSendingInterface;
use Illuminate\Support\Facades\Http;

class ElitBuzz extends SmsSendingBase implements SmsSendingInterface
{
    /**
     * Parse sms type
     *
     * @param  string  $type
     * @return int
     */
    private function getType($type)
    {
        $types = [
            'text' => 'text',
            'unicode' => 'unicode',
        ];

        return $types[$type] ?? 'text';
    }

    /**
     * Send SMS
     *
     * @return RouteMobile
     */
    public function send(array|string $to, string $message, ?string $sender = null, ?string $type = null)
    {
        $endPoint = $this->serverApiLink;

        $params = http_build_query([
            'api_key' => $this->serverApiKey,
            'senderid' => $sender,
            'contacts' => $to,
            'msg' => $message,
            'type' => $this->getType($type),
        ]);

        // Call the API and get the response
        $this->response = Http::get($endPoint, $params);

        return $this->checkResponse($this->response);
    }

    /**
     * Check response
     *
     * @param  \Illuminate\Http\Client\Response  $response
     * @return \App\Http\Services\SmsServers\Servers\RouteMobile
     */
    private function checkResponse($response)
    {

        if (! $response->successful()) {
            // todo: retry method call
            $this->error = true;
        }

        return $this;
    }
}
