<?php

namespace App\Http\Services\SmsServers\Servers;

use App\Http\Services\SmsServers\SmsSendingBase;
use App\Http\Services\SmsServers\SmsSendingInterface;
use Illuminate\Support\Facades\Http;

class RouteMobile extends SmsSendingBase implements SmsSendingInterface
{
    /**
     * Parse sms type
     *
     * @param  string  $type
     * @return int
     */
    private function getType($type)
    {
        $types = [
            'text' => 0,
            'flash' => 1,
            'unicode' => 2,
            'reserved' => 3,
        ];

        return $types[$type] ?? 0;
    }

    /**
     * Send SMS
     *
     * @param array $
     * @return mixed|void
     */
    public function send(array|string $to, string $message, ?string $sender = null, ?string $type = null)
    {
        $endPoint = $this->serverApiLink;

        $params = http_build_query([
            'username' => $this->serverUsername,
            'password' => $this->serverPassword,
            'source' => $sender,
            'destination' => $to,
            'message' => $message,
            'type' => $this->getType($type),
        ]);

        $api = "{$endPoint}?{$params}";

        $this->response = Http::get($api);

        return $this->checkResponse($this->response);
    }

    /**
     * Check response
     *
     * @param  \Illuminate\Http\Client\Response  $response
     * @return \App\Http\Services\SmsServers\Servers\RouteMobile
     */
    private function checkResponse($response)
    {
        $body = explode('|', $response->body());

        if (! $response->successful()) {
            $this->error = true;
        }

        if ($body[0] != 1701) {
            $this->error = true;
        }

        return $this;
    }
}
