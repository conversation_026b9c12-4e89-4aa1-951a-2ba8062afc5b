<?php

namespace App\Http\Services\SmsServers;

use App\Http\Services\SmsServers\SmsSendingBase;
use App\Http\Services\SmsServers\SmsSendingInterface;
use App\Models\Server;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DynamicSmsGateway extends SmsSendingBase implements SmsSendingInterface
{
    /**
     * Send SMS using dynamic gateway configuration
     *
     * @param array|string $to
     * @param string $message
     * @param string|null $sender
     * @param string|null $type
     * @return $this
     */
    public function send(array|string $to, string $message, ?string $sender = null, ?string $type = null)
    {
        try {
            $server = self::$server;
            
            if (!$server) {
                $this->error = true;
                Log::error('DynamicSmsGateway: No server configuration provided');
                return $this;
            }

            // Prepare message data for parameter replacement
            $messageData = [
                'to' => is_array($to) ? implode(',', $to) : $to,
                'message' => $message,
                'sender' => $sender,
                'type' => $type,
                'type_mapped' => $this->mapMessageType($type, $server),
                'timestamp' => now()->timestamp,
                'batch_id' => uniqid(),
                'company_id' => auth()->user()?->companies?->first()?->id ?? null
            ];

            // Get API parameters and headers with placeholders replaced
            $parameters = $server->getApiParameters($messageData);
            $headers = $server->getHeaders($messageData);

            // Make HTTP request based on server configuration
            $response = $this->makeHttpRequest($server, $parameters, $headers);
            
            $this->response = $response;
            
            return $this->checkResponse($response, $server);
            
        } catch (\Exception $e) {
            Log::error('DynamicSmsGateway: Error sending SMS', [
                'error' => $e->getMessage(),
                'server_id' => self::$server?->id,
                'to' => $to,
                'message' => substr($message, 0, 100) // Log first 100 chars only
            ]);
            
            $this->error = true;
            return $this;
        }
    }

    /**
     * Make HTTP request based on server configuration
     */
    private function makeHttpRequest(Server $server, array $parameters, array $headers)
    {
        $httpClient = Http::withHeaders($headers);
        
        // Add timeout and retry configuration
        $httpClient = $httpClient->timeout(30)->retry(2, 1000);
        
        switch (strtoupper($server->http_method)) {
            case 'POST':
                return $httpClient->post($server->api_link, $parameters);
                
            case 'PUT':
                return $httpClient->put($server->api_link, $parameters);
                
            case 'PATCH':
                return $httpClient->patch($server->api_link, $parameters);
                
            case 'GET':
            default:
                // For GET requests, add parameters as query string
                $queryString = http_build_query($parameters);
                $url = $server->api_link . (strpos($server->api_link, '?') !== false ? '&' : '?') . $queryString;
                return $httpClient->get($url);
        }
    }

    /**
     * Map message type to provider-specific format
     */
    private function mapMessageType(?string $type, Server $server): string
    {
        if (!$type) {
            return 'text';
        }

        // For legacy servers, use existing mapping logic
        if ($server->isLegacy()) {
            return $this->getLegacyTypeMapping($type, $server->name);
        }

        // For new dynamic servers, check if type is supported
        if ($server->supportsMessageType($type)) {
            return $type;
        }

        // Default to text if type not supported
        return 'text';
    }

    /**
     * Get legacy type mapping for backward compatibility
     */
    private function getLegacyTypeMapping(string $type, string $serverName): string
    {
        $mappings = [
            'RouteMobile' => [
                'text' => '0',
                'flash' => '1',
                'unicode' => '2',
                'reserved' => '3',
            ],
            'ElitBuzz' => [
                'text' => 'text',
                'unicode' => 'unicode',
            ]
        ];

        return $mappings[$serverName][$type] ?? ($mappings[$serverName]['text'] ?? 'text');
    }

    /**
     * Check response based on server configuration
     */
    private function checkResponse($response, Server $server)
    {
        if (!$response->successful()) {
            $this->error = true;
            Log::warning('DynamicSmsGateway: HTTP request failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'server_id' => $server->id
            ]);
            return $this;
        }

        $responseBody = $response->body();
        
        // Check for success pattern if configured
        if ($server->success_response_pattern) {
            if (!$this->matchesPattern($responseBody, $server->success_response_pattern)) {
                $this->error = true;
                Log::warning('DynamicSmsGateway: Response does not match success pattern', [
                    'pattern' => $server->success_response_pattern,
                    'response' => $responseBody,
                    'server_id' => $server->id
                ]);
            }
        }

        // Check for error pattern if configured
        if ($server->error_response_pattern) {
            if ($this->matchesPattern($responseBody, $server->error_response_pattern)) {
                $this->error = true;
                Log::warning('DynamicSmsGateway: Response matches error pattern', [
                    'pattern' => $server->error_response_pattern,
                    'response' => $responseBody,
                    'server_id' => $server->id
                ]);
            }
        }

        return $this;
    }

    /**
     * Check if response matches a pattern
     */
    private function matchesPattern(string $response, string $pattern): bool
    {
        // Simple string contains check for now
        // Can be enhanced to support regex patterns
        if (strpos($pattern, '/') === 0 && strrpos($pattern, '/') === strlen($pattern) - 1) {
            // Treat as regex pattern
            return preg_match($pattern, $response);
        }
        
        // Treat as simple string contains
        return strpos($response, $pattern) !== false;
    }
}
