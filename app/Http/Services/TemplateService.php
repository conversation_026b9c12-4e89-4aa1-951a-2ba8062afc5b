<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\TemplateRepository;
use App\Models\Template;
use Illuminate\Http\Request;

class TemplateService
{
    private $template;

    public function __construct(?TemplateRepository $repo = null)
    {
        $this->template = $repo ?: new TemplateRepository;
    }

    public function store(Request $request): void
    {
        $templateData = $request->only('name', 'text', 'is_pattern');

        $templateData = array_merge($templateData, [
            'user_id' => auth()->user()->id,
            'status' => 'enabled',
        ]);

        try {
            $this->template->store($templateData);
        } catch (\Exception $e) {
            throw new ErrorMessageException('Templates Create Error: '.$e->getMessage());
        }
    }

    public function update(Request $request, Template $template): void
    {
        $templateData = $request->only('name', 'text', 'is_pattern');

        try {
            $this->template->setContext($template)->update(array_filter($templateData));
        } catch (\Exception $e) {
            throw new ErrorMessageException('Template update Error: '.$e->getMessage());
        }
    }

    public function delete(Template $templates): void
    {
        $this->template->setContext($templates)->delete();
    }
}
