<?php

namespace App\Http\Services;

use App\Exceptions\ErrorMessageException;
use App\Http\Repositories\CoverageRepository;
use App\Models\Coverage;
use Illuminate\Http\Request;

class CoverageService
{
    private $covarage;

    public function __construct(?CoverageRepository $repo = null)
    {
        $this->covarage = $repo ?: new CoverageRepository;
    }

    /**
     * Store Coverage
     *
     * @throws ErrorMessageException
     */
    public function store(Request $request)
    {
        try {
            $coverageData = $request->only(['prefix', 'masking_price', 'non_masking_price', 'operator', 'server_id',
                'status']);

            $this->covarage->store($coverageData);
        } catch (\Exception $e) {
            throw new ErrorMessageException('Coverage Create Error: '.$e->getMessage());
        }
    }

    /**
     * Delete Coverage
     */
    public function delete($covarage)
    {
        $this->covarage->setContext($covarage)->delete();
    }

    /**
     * Update Coverage
     *
     * @throws ErrorMessageException
     */
    public function update(Request $request, Coverage $coverage)
    {
        try {
            $coverageData = $request->only(['prefix', 'masking_price', 'non_masking_price', 'operator', 'server_id',
                'status']);

            $this->covarage->setContext($coverage)->update(array_filter($coverageData));
        } catch (\Exception $e) {
            throw new ErrorMessageException('Coverage Update Error: '.$e->getMessage());
        }
    }
}
