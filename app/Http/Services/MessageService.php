<?php

namespace App\Http\Services;

use App\Helpers\Format;
use App\Helpers\Parser;
use App\Helpers\SmsHelper;
use App\Http\Repositories\ContactRepository;
use App\Http\Repositories\CoverageRepository;
use App\Http\Repositories\MessageRepository;
use App\Http\Repositories\SenderRepository;
use App\Models\Sender;
use Carbon\Carbon;
use Illuminate\Http\Request;

class MessageService
{
    /**
     * Message repository
     *
     * @var \App\Http\Repositories\MessageRepository|null
     */
    private $message;

    /**
     * Coverage repository
     *
     * @var \App\Http\Repositories\CoverageRepository|null
     */
    private $coverage;

    /**
     * Sender repository
     *
     * @var \App\Http\Repositories\SenderRepository|null
     */
    private $sender;

    /**
     * Contact repository
     *
     * @var \App\Http\Repositories\ContactRepository|null
     */
    private $contact;

    public function __construct(?MessageRepository $repo = null)
    {
        $this->message = $repo ?: new MessageRepository;
        $this->coverage = new CoverageRepository;
        $this->sender = new SenderRepository;
        $this->contact = new ContactRepository;
    }

    public function validatePhoneNumber($number)
    {
        $number = preg_replace(["/[\s+]/", '/[^0-9]/'], '', $number);
        $number = $this->resolveCountryCodeForValidNumber($number);

        return $number;
    }

    public function messageCount($text, $messageType)
    {
        $allowedTypes = ['text', 'unicode'];

        // Determine the character count based on the message type
        $textCount = (in_array($messageType, $allowedTypes) && mb_detect_encoding($text, 'UTF-8', true)) ? 60 : 160;

        return ceil(mb_strlen($text) / $textCount);
    }

    public function resolveCountryCodeForValidNumber($number)
    {
        if (! preg_match('/^88/', $number)) {
            $number = "88{$number}";
        }

        if (strlen($number) !== 13) {
            return null;
        }

        return $number;
    }

    public function findValidNumbers($numbers): array
    {
        $numbers = explode("\r\n", trim($numbers));
        foreach ($numbers as &$number) {
            $number = $this->validatePhoneNumber($number);
        }

        return array_filter($numbers);
    }

    public function store(Request $request): array
    {
        $storeFormat = [];

        $numbers = $this->findValidNumbers($request->phone_numbers);
        $coverageAll = $this->coverage->getAll();
        $sender = $this->sender->getWithoutGlobalScopes($request->sender_id);

        $coverages = $coverageAll->groupBy('prefix')->toArray();
        $batchNumber = Parser::random('messages', 'batch_number');

        $priceColumn = ! empty($sender->is_masking) ? 'masking_price' : 'non_masking_price';
        $messageCount = $this->messageCount($request->sms_content, $request->sms_type);

        // Fetch all defaultSenders grouped by server_id
        $defaultSenders = $this->sender->getDefaultSenders($coverages);

        foreach ($numbers as $number) {
            if (! isset($coverages[substr($number, 0, 5)])) {
                continue;
            }

            $coverage = current($coverages[substr($number, 0, 5)]);

            $price = $messageCount * $coverage[$priceColumn];

            // Logic for determining sender_id and server_id
            $server_id = $coverage['server_id'];
            $sender_id = $sender->id;

            if ($sender->is_masking) {
                $server_id = $sender->server_id ?? $coverage['server_id'];
            } else {
                // Check if there is a defaultSender for this server_id
                if (isset($defaultSenders[$coverage['server_id']]) && $defaultSender = $defaultSenders[$coverage['server_id']]->first()) {
                    $sender_id = $defaultSender->id;
                }
            }

            $storeFormat[] = [
                'sender_id' => $sender_id,
                'phone_number' => $number,
                'operator' => $coverage['operator'],
                'server_id' => $server_id,
                'sms_type' => $request->sms_type,
                'sms_content' => $request->sms_content,
                'sms_count' => $messageCount,
                'sms_cost' => $price,
                'batch_number' => $batchNumber,
                'user_id' => $request->has('user_id') ? $request->user_id : auth()->user()->id,
                'status' => ! empty($request->is_scheduled) ? 'scheduled' : 'pending',
                'schedule_at' => ! empty($request->is_scheduled) ? Carbon::create($request->scheduled_at) : null,
            ];
        }

        if (count($storeFormat) < 1) {
            return [
                'status' => 'failed',
                'message' => 'No valid phone number has been found.',
            ];
        }

        $messages = $this->message->storeMany($storeFormat);

        if (empty($request->is_scheduled)) {
            $messages = $this->message->getMessagesByBatch($batchNumber);

            return [
                'status' => 'success',
                'message' => 'Messages are being sent.',
                'data' => $this->sendInstantMessage($messages),
            ];
        }

        return [
            'status' => 'success',
            'message' => 'Messages have been scheduled successfully.',
            'data' => $messages,
        ];
    }

    /**
     * Store messages
     */
    public function bcstore(Request $request)// : array
    {
        $storeFormat = [];

        $numbers = $this->findValidNumbers($request->phone_numbers);
        $coverageAll = $this->coverage->getAll();
        $sender = $this->sender->getWithoutGlobalScopes($request->sender_id);

        $coverages = $coverageAll->groupBy('prefix')->toArray();
        $batchNumber = Parser::random('messages', 'batch_number');

        $priceColumn = ! empty($sender->is_masking) ? 'masking_price' : 'non_masking_price';
        $messageCount = $this->messageCount($request->sms_content, $request->sms_type);

        foreach ($numbers as $number) {
            if (! isset($coverages[substr($number, 0, 5)])) {
                continue;
            }

            $coverage = current($coverages[substr($number, 0, 5)]);

            $price = $messageCount * $coverage[$priceColumn];

            // if $sender->is_masking is true then server_id will be $sender->server_id
            // if $sender->is_masking is false then need change $sender_id by bellow query
            // $defaultSender = Sender::where('is_server_default', 1)->where('server_id', $coverage['server_id'])->first()
            // sender_id = $defaultSender->id;

            $storeFormat[] = [
                'sender_id' => $sender->id,
                'phone_number' => $number,
                'operator' => $coverage['operator'],
                'server_id' => $coverage['server_id'],
                'sms_type' => $request->sms_type,
                'sms_content' => $request->sms_content,
                'sms_count' => $messageCount,
                'sms_cost' => $price,
                'batch_number' => $batchNumber,
                'user_id' => $request->has('user_id') ? $request->user_id : auth()->user()->id,
                'status' => ! empty($request->is_scheduled) ? 'scheduled' : 'pending',
                'schedule_at' => ! empty($request->is_scheduled) ? Carbon::create($request->scheduled_at) : null,
            ];
        }

        if (count($storeFormat) < 1) {
            return [
                'status' => 'failed',
                'message' => 'No valid phone number has been found.',
            ];
        }

        $messages = $this->message->storeMany($storeFormat);

        if (empty($request->is_scheduled)) {
            $messages = $this->message->getMessagesByBatch($batchNumber);

            return [
                'status' => 'success',
                'message' => 'Messages are being sent.',
                'data' => $this->sendInstantMessage($messages),
            ];
        }

        return [
            'status' => 'success',
            'message' => 'Messages have been scheduled successfully.',
            'data' => $messages,
        ];
    }

    /**
     * Update messages
     */
    public function update(Request $request, $id) {}

    public function sendInstantMessage($messages): array
    {
        $response = [];
        foreach ($messages as $message) {
            $response[] = SmsHelper::sendSms($message->id);
        }

        return $response;
    }

    public function dynamicSms(Request $request, $data)
    {
        $collection = Format::formatDynamicSmsData($data);

        foreach ($collection as $set) {
            $text = Format::replaceVariables($set, $request->sms_content);

            $this->store(new Request([
                'sender_id' => $request->sender_id,
                'phone_numbers' => $set['mobile'],
                'sms_type' => 'text',
                'sms_content' => $text,
                'is_scheduled' => $request->is_scheduled,
                'scheduled_at' => $request->scheduled_at,
            ]));
        }
    }

    public function groupSms(Request $request)
    {
        $contacts = $this->contact->getByGroup($request->group);

        $params = $request->except(['group']) + ['phone_numbers' => $contacts->pluck('phone')->implode("\r\n")];

        $this->store(new Request($params));
    }
}
