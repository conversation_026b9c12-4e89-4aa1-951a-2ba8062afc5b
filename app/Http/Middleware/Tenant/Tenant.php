<?php

namespace App\Http\Middleware\Tenant;

use App\Exceptions\ErrorMessageException;
use App\Models\Company;
use App\Tenant\Manager;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Tenant
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $company = auth()->user()->companies()->first();

        $tenant = $this->resolveTenant(
            $company->id ?: session()->get('tenant')
        );

        if (empty($tenant->id)) {
            throw new ErrorMessageException('Company not found');
        }

        $this->registerTenant($tenant);

        return $next($request);
    }

    /**
     * register Tenant
     *
     * @return void
     */
    protected function registerTenant($tenant)
    {
        app(Manager::class)->setTenant($tenant);

        session()->put('tenant', $tenant->id);
    }

    /**
     * Find the company by id
     *
     * @return mixed
     */
    protected function resolveTenant($id)
    {
        return Company::find($id);
    }
}
