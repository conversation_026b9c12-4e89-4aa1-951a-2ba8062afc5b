<?php

namespace App\Http\Middleware\Tenant;

use App\Exceptions\SmsSendingResponseException;
use App\Http\Services\SendSmsService;
use App\Tenant\Manager;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiTenant
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $service = new SendSmsService;

        $company = $service->findCompany($request);

        if (empty($company)) {
            throw new SmsSendingResponseException(1003);
        }

        $this->registerTenant($company);

        return $next($request);
    }

    /**
     * register Tenant
     *
     * @return void
     */
    protected function registerTenant($tenant)
    {
        app(Manager::class)->setTenant($tenant);
    }
}
