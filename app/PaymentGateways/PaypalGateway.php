<?php

namespace App\PaymentGateways;

use PayPal\Api\Amount;
use PayPal\Api\Details;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Transaction;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;

class PaypalGateway
{
    protected $apiContext;

    public function __construct()
    {
        $paypalClientId = env('PAYPAL_CLIENT_ID');
        $paypalSecret = env('PAYPAL_SECRET');
        $this->apiContext = new ApiContext(
            new OAuthTokenCredential($paypalClientId, $paypalSecret)
        );
    }

    public function processPayment($amount)
    {
        $payer = new Payer;
        $payer->setPaymentMethod('paypal');

        $item = new Item;
        $item->setName('Monthly Subscription')
            ->setCurrency('USD')
            ->setQuantity(1)
            ->setPrice($amount);

        $itemList = new ItemList;
        $itemList->setItems([$item]);

        $details = new Details;
        $details->setSubtotal($amount);

        $amount = new Amount;
        $amount->setCurrency('USD')
            ->setTotal($amount)
            ->setDetails($details);

        $transaction = new Transaction;
        $transaction->setAmount($amount)
            ->setItemList($itemList)
            ->setDescription('Monthly Subscription');

        $redirectUrls = new RedirectUrls;
        $redirectUrls->setReturnUrl('http://example.com/success')
            ->setCancelUrl('http://example.com/cancel');

        $payment = new Payment;
        $payment->setIntent('sale')
            ->setPayer($payer)
            ->setRedirectUrls($redirectUrls)
            ->setTransactions([$transaction]);

        $payment->create($this->apiContext);

        return $payment->getApprovalLink();
    }

    public function executePayment($paymentId, $payerId)
    {
        $payment = Payment::get($paymentId, $this->apiContext);

        $execution = new PaymentExecution;
        $execution->setPayerId($payerId);

        $result = $payment->execute($execution, $this->apiContext);

        return $result;
    }
}
