<?php

namespace App\PaymentGateways;

use Stripe\Charge;
use Stripe\Customer;
use Stripe\Stripe;

class StripeGateway
{
    public function processPayment($amount, $token)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));

        $customer = Customer::create([
            'email' => auth()->user()->email,
            'source' => $token,
        ]);

        $charge = Charge::create([
            'customer' => $customer->id,
            'amount' => $amount,
            'currency' => 'usd',
            'description' => 'Monthly Subscription',
        ]);

        return $charge->status;
    }
}
