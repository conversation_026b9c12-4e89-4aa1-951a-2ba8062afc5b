<?php

namespace App\Observers;

use App\Models\Company;
use App\Models\Transaction;

class CompanyObserver
{
    public function updated(Company $company)
    {
        $originalBalance = $company->getOriginal('current_balance');
        $newBalance = $company->getAttribute('current_balance');

        $changeInBalance = $newBalance - $originalBalance;

        if ($changeInBalance !== 0) {
            $transaction = new Transaction([
                'user_id' => 1,
                'company_id' => $company->id,
                'in' => $changeInBalance > 0 ? $changeInBalance : 0,
                'out' => $changeInBalance < 0 ? $changeInBalance : 0,
                'amount' => abs($changeInBalance),
                'log' => $company->remarks,
            ]);

            $company->transactions()->save($transaction);
        }
    }
}
