<?php

namespace App\Events;

use App\Models\User;
use Illuminate\Auth\Events\Registered as BaseRegistered;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRegistered extends BaseRegistered
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * The authenticated user.
     *
     * @var \Illuminate\Contracts\Auth\Authenticatable
     */
    public $user;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user)
    {
        parent::__construct($user);
        $this->user = $user;
    }
}
