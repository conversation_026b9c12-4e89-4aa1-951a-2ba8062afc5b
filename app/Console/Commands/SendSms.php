<?php

namespace App\Console\Commands;

use App\Http\Repositories\MessageRepository;
use App\Jobs\SendSmsJob;
use Illuminate\Console\Command;

class SendSms extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:sms';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Sms';

    /**
     * Execute the console command.
     */
    public function handle(MessageRepository $messages)
    {
        $pendingMessages = $messages->getScheduledMessages();

        if ($pendingMessages->count() < 1) {
            return 0;
        }

        foreach ($pendingMessages as $message) {
            SendSmsJob::dispatch($message->id);
        }
    }
}
