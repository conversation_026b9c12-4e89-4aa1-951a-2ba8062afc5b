<?php

namespace App\Jobs;

use App\Http\Repositories\MessageRepository;
use App\Http\Services\SmsServers\SmsSendingBase;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendSmsJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $message;

    private $messageId;

    private $sms;

    /**
     * Create a new job instance.
     */
    public function __construct($id)
    {
        $this->messageId = $id;

        $this->message = new MessageRepository;
        $this->sms = $this->message->getWithSenderServer($this->messageId);
        $this->message->setContext($this->sms)
            ->updateProcessingSendingMessage();

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // need to fix this
        // $sms = $this->sms;
        $sms = $this->message->getWithSenderServer($this->messageId);

        $smsResponse = SmsSendingBase::getServerInstance($sms->server->name, $sms->server)
            ->send(
                $sms->phone_number,
                $sms->sms_content,
                $sms->sender->name,
                $sms->sms_type
            );

        if ($smsResponse->error) {
            $this->message->setContext($sms)
                ->updateFailedSendingMessage($smsResponse->response->body());

            return;
        }

        $this->message->setContext($sms)
            ->updateSuccessFullMessage($smsResponse->response->body());
    }
}
