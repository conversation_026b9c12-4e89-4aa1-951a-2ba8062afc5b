<?php

namespace App\Providers;

use App\Http\Repositories\GatewayRepository;
use App\Http\Services\PaymentGateways\GatewayBindingInterface;
use App\Tenant\Manager;
use App\Tenant\Observers\TenantObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(RepositoryServiceProvider::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Registering singleton instances for Manager and TenantObserver
        $this->app->singleton(Manager::class, function () {
            return new Manager();
        });

        $this->app->singleton(TenantObserver::class, function () {
            return new TenantObserver();
        });

        // Dynamically binding GatewayBindingInterface
        $this->app->singleton(GatewayBindingInterface::class, function ($app) {
            // Ensure we are only processing payment-related requests
            if (! $app->request->is(['payment/*', 'web-api/add-balance'])) {
                return null;
            }

            // Determine the payment method from request or fall back to default (apiVersion logic)
            $paymentMethod = $app->request->input('payment_method') ?: $app->request->input('value_a');

            // If apiVersion is present, override payment method to 'BkashPay'
            if ($app->request->has('apiVersion')) {
                $paymentMethod = 'BkashPay';
            }

            // Resolve the gateway class dynamically based on the payment method
            $classFullName = "\\App\\Http\\Services\\PaymentGateways\\Gateways\\" . ucfirst($paymentMethod);

            // If the class doesn't exist, return null
            if (! class_exists($classFullName)) {
                return null;
            }

            // Fetch the gateway data from the repository
            $gatewayRepo = app(GatewayRepository::class); // Leverage dependency injection for GatewayRepository
            $gateway = $gatewayRepo->findByClassName(strtolower($paymentMethod));

            // Return the gateway instance
            return new $classFullName($app->request, $gateway);
        });
    }
}
