<?php

namespace App\Tenant\Scopes;

use App\Tenant\Manager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    protected mixed $manager;

    // Array of model class names to exclude from tenant scope
    protected $excludedModels = [
        \App\Models\Coverage::class,
    ];

    public function __construct()
    {
        $this->manager = app(Manager::class);
    }

    public function apply(Builder $builder, Model $model): void
    {
        // Check if running in console or if the user is a SuperAdmin and the model is not excluded
        if (app()->runningInConsole() || ($this->isSuperAdmin() && ! in_array(get_class($model), $this->excludedModels))) {
            return;
        }

        $tenant = $this->manager->getTenant();

        if (empty($tenant)) {
            return;
        }

        // Use the model's table name to reference the company_id column
        $builder->where($model->getTable().'.company_id', '=', $tenant->id);
    }

    /**
     * Check super admin role
     */
    protected function isSuperAdmin(): bool
    {
        return auth()->check() && auth()->user()->hasRole('super-admin');
    }
}
