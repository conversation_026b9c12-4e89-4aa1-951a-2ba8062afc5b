<?php

namespace App\Tenant\Observers;

use App\Tenant\Manager;
use Illuminate\Database\Eloquent\Model;

class TenantObserver
{
    protected $manager;

    public function __construct()
    {
        $this->manager = app(Manager::class);
    }

    public function creating(Model $model): void
    {
        // Check if running in console
        if (app()->runningInConsole()) {
            return;
        }

        $tenant = $this->manager->getTenant();
        $foreignKey = $tenant->getForeignKey();

        if (! isset($model->{$foreignKey})) {
            $model->setAttribute($foreignKey, $tenant->id);
        }
    }
}
