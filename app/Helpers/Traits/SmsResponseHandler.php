<?php

namespace App\Helpers\Traits;

/**
 * Common DataSet Traits
 */
trait SmsResponseHandler
{
    /**
     * Default error dataSet
     */
    protected static array $errorCodes = [
        '1000' => 'No SMS data found.',
        '1001' => 'Something error is happened.',
        '1002' => 'Sender Id/Masking Not Found',
        '1003' => 'Invalid API key',
        '1004' => 'SPAM Detected',
        '1005' => 'Internal Error',
        '1006' => 'Internal Error',
        '1007' => 'Balance Insufficient',
        '1008' => 'Message is empty',
        '1009' => 'Message Type Not Set (text/unicode)',
        '1010' => 'Invalid User & Password',
        '1011' => 'Invalid User Id',
        '1012' => 'Invalid Number',
        '1013' => 'API limit error',
        '1014' => 'No matching template',
        '1015' => 'SMS Content Validation Fails',
        '1016' => 'Schedule date time format is invalid',
        '1019' => 'Sms Purpose Missing',
        '69' => 'Route mobile error',
    ];

    /**
     * Default success dataSet
     */
    protected static array $successCodes = [
        '0000' => 'Successfully Send message',
        '1701' => 'Successfully Send message',
    ];
}
