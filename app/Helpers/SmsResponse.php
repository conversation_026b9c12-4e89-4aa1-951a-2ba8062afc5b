<?php

namespace App\Helpers;

use App\Helpers\Traits\SmsResponseHandler;

/**
 * Common Class Handles CommonHelper DataSet and methods
 */
class SmsResponse extends Helper
{
    use SmsResponseHandler;

    /**
     * Create error response
     *
     * @param  string  $code
     * @return array
     */
    public static function error($code, $details = null)
    {
        $set = self::errorCodes();
        $message = $set[$code] ?? 'Unknown error';

        // Add details if provided
        if ($details) {
            $message .= ' - '.$details;
        }

        return self::createResponse($code, $message);
    }

    /**
     * Create success response
     *
     * @param  string  $code
     * @param  string  $message
     * @param  string  $phoneNumber
     * @return array
     */
    public static function success($code = '0000', $message = 'SMS sent successfully!', $phoneNumber = null)
    {
        $set = self::successCodes();
        $message = $set[$code] ?? $message;

        // If phone number is provided, include it in the message
        if ($phoneNumber) {
            $message = "({$phoneNumber}: {$message})";
        }

        return self::createResponse($code, $message);
    }

    /**
     * Create success response for scheduled SMS
     *
     * @param  string  $code
     * @return array
     */
    public static function successScheduled($code = '0000')
    {
        return self::createResponse($code, 'Successfully scheduled message');
    }

    /**
     * Create response format
     *
     * @param  string  $code
     * @param  string  $message
     * @return array
     */
    public static function createResponse($code, $message)
    {
        return [
            'response_code' => $code,
            'message' => $message,
        ];
    }
}
