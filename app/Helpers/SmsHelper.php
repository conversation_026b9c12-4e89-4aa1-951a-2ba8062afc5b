<?php

namespace App\Helpers;

use App\Http\Repositories\MessageRepository;
use App\Http\Services\SmsServers\SmsSendingBase;
use Illuminate\Support\Facades\Log;

class SmsHelper
{
    /**
     * Send SMS and return a real-time response
     *
     * @param  int  $messageId
     */
    public static function sendSms($messageId): array
    {
        $messageRepo = new MessageRepository;
        $sms = $messageRepo->getWithSenderServer($messageId);

        // Check if the SMS object is valid
        if (! $sms) {
            return SmsResponse::error('1001'); // Custom error response (no valid SMS data found)
        }

        // Send the SMS and get the response from the server
        $smsResponse = SmsSendingBase::getServerInstance($sms->server->name, $sms->server)
            ->send(
                $sms->phone_number,
                $sms->sms_content,
                $sms->sender->name,
                $sms->sms_type
            );

        // Check for error in the response body
        $responseBody = $smsResponse->response->body();

        if ($smsResponse->error || empty($responseBody)) {
            // Log error and update message as failed
            Log::error("SMS sending failed for message ID {$messageId}. Response: {$responseBody}");

            $messageRepo->setContext($sms)
                ->updateFailedSendingMessage($responseBody);

            return SmsResponse::error($responseBody); // Returning error response
        }

        // Update message as successfully sent
        $messageRepo->setContext($sms)->updateSuccessFullMessage($responseBody);

        return SmsResponse::success('0000', $responseBody, $sms->batch_number); // Success response
    }
}
