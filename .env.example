APP_NAME=ShareSpeedy
APP_ENV=local
APP_KEY=base64:SdmtY3PTyTXIvb+OemiR0HSGx3kTe8vr8c7eHvT/v6w=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000
APP_PORTL=8080
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=sharespeedy
DB_USERNAME=root
DB_PASSWORD=

# DNS Configuration
DNS_SERVER_IP=*************

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp-relay.brevo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=pLbGyxNBrhCHns8J
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


BKASH_SANDBOX=true  #for production use false

BKASH_APP_KEY=""
BKASH_APP_SECRET=""
BKASH_USERNAME=""
BKASH_PASSWORD=""

#for multi account
BKASH_APP_KEY_2=""
BKASH_APP_SECRET_2=""
BKASH_USERNAME_2=""
BKASH_PASSWORD_2=""

#so on just use _number likes _3, _4, _5

BKASH_CALLBACK_URL=""


SSLCZ_TESTMODE=true
IS_LOCALHOST=true
SSLCZ_STORE_ID=***********66
SSLCZ_STORE_PASSWORD=***********
