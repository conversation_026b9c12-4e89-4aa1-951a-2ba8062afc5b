@extends('layouts.app')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')

        <!--begin::Domain Management-->
        <div class="card mb-5 mb-xl-10">
            <!--begin::Card header-->
            <div class="card-header border-0 cursor-pointer">
                <!--begin::Card title-->
                <div class="card-title m-0">
                    <h3 class="fw-bold m-0">Domain Management</h3>
                </div>
                <!--end::Card title-->
                <!--begin::Action-->
                <div class="card-toolbar">
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_add_domain">
                        Add Domain
                    </button>
                </div>
                <!--end::Action-->
            </div>
            <!--end::Card header-->

            <!--begin::Content-->
            <div class="card-body border-top p-9">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if($domains->count() > 0)
                    <!--begin::Table-->
                    <div class="table-responsive">
                        <table class="table table-row-dashed table-row-gray-300 gy-7">
                            <thead>
                                <tr class="fw-bold fs-6 text-gray-800 border-bottom-2 border-gray-200">
                                    <th>Domain</th>
                                    <th>DNS Status</th>
                                    <th>Last Verified</th>
                                    <th>Added Date</th>
                                    <th class="text-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($domains as $domain)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-45px me-5">
                                                    <span class="symbol-label bg-light-primary text-primary fw-bold">
                                                        {{ strtoupper(substr($domain->domain, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-start flex-column">
                                                    <span class="text-dark fw-bold text-hover-primary fs-6">{{ $domain->domain }}</span>
                                                    @if($domain->isPending())
                                                        <small class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#kt_modal_dns_instructions_{{ $domain->id }}">
                                                                View DNS Setup Instructions
                                                            </a>
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge {{ $domain->getStatusBadgeClass() }}">{{ $domain->getStatusText() }}</span>
                                            @if($domain->isFailed() && $domain->verification_notes)
                                                <div class="text-muted fs-7 mt-1">{{ $domain->verification_notes }}</div>
                                            @endif
                                        </td>
                                        <td>
                                            @if($domain->last_verified_at)
                                                <span class="text-muted fw-semibold d-block fs-7">{{ $domain->last_verified_at->format('M d, Y H:i') }}</span>
                                            @else
                                                <span class="text-muted fw-semibold d-block fs-7">Never</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted fw-semibold text-muted d-block fs-7">{{ $domain->created_at->format('M d, Y') }}</span>
                                        </td>
                                        <td class="text-end">
                                            <div class="d-flex justify-content-end">
                                                @if($domain->isPending())
                                                    <button type="button" class="btn btn-sm btn-light-primary me-2"
                                                            data-bs-toggle="modal" data-bs-target="#kt_modal_dns_instructions_{{ $domain->id }}">
                                                        <i class="fas fa-cog"></i> Setup DNS
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-light-info me-2"
                                                        onclick="verifyDomain({{ $domain->id }})">
                                                    <i class="fas fa-sync"></i> Verify
                                                </button>
                                                <form action="{{ route('account.domains.destroy', $domain->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-light-danger"
                                                            onclick="return confirm('Are you sure you want to delete this domain?')">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!--end::Table-->
                @else
                    <!--begin::Empty state-->
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <img src="{{ asset('media/illustrations/sketchy-1/4.png') }}" class="mw-200px" alt="">
                        </div>
                        <div class="mb-3">
                            <h3 class="text-gray-800 fs-3 fw-bold mb-2">No domains found</h3>
                            <div class="text-gray-400 fs-6 fw-semibold">You haven't added any domains yet. Click the "Add Domain" button to get started.</div>
                        </div>
                    </div>
                    <!--end::Empty state-->
                @endif
            </div>
            <!--end::Content-->
        </div>
        <!--end::Domain Management-->
    </div>
    <!--end::Container-->

    <!--begin::Modal - Add Domain-->
    <div class="modal fade" id="kt_modal_add_domain" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered mw-650px">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="fw-bold">Add New Domain</h2>
                    <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                        <span class="svg-icon svg-icon-1">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                            </svg>
                        </span>
                    </div>
                </div>

                <form action="{{ route('account.domains.store') }}" method="POST">
                    @csrf
                    <div class="modal-body py-10 px-lg-17">
                        <div class="scroll-y me-n7 pe-7" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_domain_header" data-kt-scroll-wrappers="#kt_modal_add_domain_scroll" data-kt-scroll-offset="300px">
                            <div class="fv-row mb-7">
                                <label class="required fs-6 fw-semibold mb-2">Domain Name</label>
                                <input type="text" class="form-control form-control-solid @error('domain') is-invalid @enderror"
                                       name="domain" value="{{ old('domain') }}" placeholder="example.com" required />
                                @error('domain')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Enter the domain name without http:// or https://</div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer flex-center">
                        <button type="button" class="btn btn-light me-3" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Domain</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!--end::Modal - Add Domain-->

    <!--begin::DNS Instructions Modals-->
    @foreach($domains as $domain)
        @if($domain->isPending())
            <div class="modal fade" id="kt_modal_dns_instructions_{{ $domain->id }}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 class="fw-bold">DNS Setup Instructions for {{ $domain->domain }}</h2>
                            <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                                <span class="svg-icon svg-icon-1">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                                    </svg>
                                </span>
                            </div>
                        </div>

                        <div class="modal-body py-10 px-lg-17">
                            <div class="alert alert-info">
                                <div class="alert-text">
                                    <strong>Important:</strong> To activate your domain, you need to point it to our server by adding an A record in your DNS settings.
                                </div>
                            </div>

                            <!--begin::DNS Record Info-->
                            <div class="card mb-7">
                                <div class="card-header">
                                    <h3 class="card-title">DNS Record Information</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="fw-bold">Record Type:</label>
                                            <div class="text-muted">A Record</div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="fw-bold">Host/Name:</label>
                                            <div class="text-muted">@ (root domain)</div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="fw-bold">Points to/Value:</label>
                                            <div class="text-primary fw-bold">{{ config('dns.server_ip') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end::DNS Record Info-->

                            <!--begin::Instructions Tabs-->
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#general_{{ $domain->id }}">General</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#cloudflare_{{ $domain->id }}">Cloudflare</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#godaddy_{{ $domain->id }}">GoDaddy</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#namecheap_{{ $domain->id }}">Namecheap</a>
                                </li>
                            </ul>

                            <div class="tab-content">
                                @foreach(config('dns.instructions') as $provider => $instruction)
                                    <div class="tab-pane fade {{ $provider === 'general' ? 'show active' : '' }}" id="{{ $provider }}_{{ $domain->id }}">
                                        <h4>{{ $instruction['title'] }}</h4>
                                        <ol class="list-group list-group-numbered">
                                            @foreach($instruction['steps'] as $step)
                                                <li class="list-group-item border-0 ps-0">{{ $step }}</li>
                                            @endforeach
                                        </ol>
                                    </div>
                                @endforeach
                            </div>
                            <!--end::Instructions Tabs-->

                            <div class="alert alert-warning mt-5">
                                <div class="alert-text">
                                    <strong>Note:</strong> DNS changes can take up to 24-48 hours to propagate worldwide. After making the changes, click the "Verify" button to check if your domain is properly configured.
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="verifyDomain({{ $domain->id }})" data-bs-dismiss="modal">
                                Verify Domain Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @endforeach
    <!--end::DNS Instructions Modals-->

    <script>
        function verifyDomain(domainId, forceVerify = false) {
            // Show loading state
            const verifyBtn = document.querySelector(`button[onclick="verifyDomain(${domainId})"]`);
            const originalText = verifyBtn.innerHTML;
            verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
            verifyBtn.disabled = true;

            // Make AJAX request to verify domain
            const url = forceVerify ? `/account/domains/${domainId}/verify?force=1` : `/account/domains/${domainId}/verify`;
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and reload page
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'Verification Failed',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while verifying the domain.'
                });
            })
            .finally(() => {
                // Restore button state
                verifyBtn.innerHTML = originalText;
                verifyBtn.disabled = false;
            });
        }
    </script>
@endsection
