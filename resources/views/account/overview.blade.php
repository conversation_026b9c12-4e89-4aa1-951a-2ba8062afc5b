@extends('layouts.app')

@section('content')
    @php
        $user = Auth::user();
        $isUserRole = $user->hasRole('user');
    @endphp

    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')

        @if($isUserRole)
            {{-- User Role: Personal Information Only --}}
            <!--begin::Personal Profile Card-->
            <div class="card mb-5 mb-xl-10">
                <!--begin::Card header-->
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="fw-bold m-0">Personal Profile</h3>
                    </div>
                    <a href="{{ route('account.settings') }}" class="btn btn-sm btn-primary">Edit Profile</a>
                </div>
                <!--end::Card header-->

                <!--begin::Card body-->
                <div class="card-body p-9">
                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Full Name</label>
                        <div class="col-lg-8">
                            <span class="fw-bold fs-6 text-gray-800">{{ $user->name }}</span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Email Address</label>
                        <div class="col-lg-8 d-flex align-items-center">
                            <span class="fw-bold fs-6 text-gray-800 me-2">{{ $user->email }}</span>
                            <span class="badge {{ $user->email_verified_at ? 'badge-light-success' : 'badge-light-warning' }}">
                                {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Phone Number</label>
                        <div class="col-lg-8">
                            <span class="fw-bold fs-6 text-gray-800">{{ $user->phone }}</span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Username</label>
                        <div class="col-lg-8">
                            <span class="fw-bold fs-6 text-gray-800">{{ $user->username }}</span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Account Status</label>
                        <div class="col-lg-8">
                            <span class="badge {{ $user->status ? 'badge-light-success' : 'badge-light-danger' }}">
                                {{ $user->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <label class="col-lg-4 fw-semibold text-muted">Member Since</label>
                        <div class="col-lg-8">
                            <span class="fw-bold fs-6 text-gray-800">{{ $user->created_at->format('F j, Y') }}</span>
                        </div>
                    </div>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Personal Profile Card-->

        @else
            {{-- Company-Associated Roles: Company + Personal Information --}}



            <!--begin::Row-->
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <!--begin::Col-->
                <div class="col-xxl-6">
                    <!--begin::Company Information Card-->
                    <div class="card card-xxl-stretch mb-5 mb-xl-8">
                        <!--begin::Header-->
                        <div class="card-header border-0 pt-5">
                            <h3 class="card-title align-items-start flex-column">
                                <span class="card-label fw-bold fs-3 mb-1">Company Information</span>
                                <span class="text-muted mt-1 fw-semibold fs-7">Business details and settings</span>
                            </h3>
                            <div class="card-toolbar">
                                <a href="{{ route('account.settings') }}" class="btn btn-sm btn-light-primary">
                                    <i class="ki-duotone ki-pencil fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    Edit
                                </a>
                            </div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="card-body py-3">
                            <!--begin::Table container-->
                            <div class="table-responsive">
                                <!--begin::Table-->
                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                    <!--begin::Table body-->
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold text-muted min-w-100px">Company Name</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $company->name ?? 'N/A' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Status</td>
                                            <td class="text-end">
                                                <span class="badge {{ $company->status === 'enabled' ? 'badge-light-success' : 'badge-light-danger' }}">
                                                    {{ ucfirst($company->status ?? 'Unknown') }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">API Key</td>
                                            <td class="text-gray-900 fw-bold text-end">
                                                <span class="text-muted">{{ substr($company->api_key ?? '', 0, 8) }}...</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Minimum Recharge</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ number_format($company->minimum_recharge_amount ?? 0, 2) }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Member Since</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $company->created_at ? $company->created_at->format('M j, Y') : 'N/A' }}</td>
                                        </tr>
                                    </tbody>
                                    <!--end::Table body-->
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table container-->
                        </div>
                        <!--begin::Body-->
                    </div>
                    <!--end::Company Information Card-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xxl-6">
                    <!--begin::Contact Information Card-->
                    <div class="card card-xxl-stretch mb-5 mb-xl-8">
                        <!--begin::Header-->
                        <div class="card-header border-0 pt-5">
                            <h3 class="card-title align-items-start flex-column">
                                <span class="card-label fw-bold fs-3 mb-1">Contact Information</span>
                                <span class="text-muted mt-1 fw-semibold fs-7">Primary contact details</span>
                            </h3>
                            <div class="card-toolbar">
                                <a href="{{ route('account.settings') }}" class="btn btn-sm btn-light-primary">
                                    <i class="ki-duotone ki-pencil fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    Edit
                                </a>
                            </div>
                        </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="card-body py-3">
                            <!--begin::Table container-->
                            <div class="table-responsive">
                                <!--begin::Table-->
                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                    <!--begin::Table body-->
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold text-muted min-w-100px">Full Name</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $user->name }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Email</td>
                                            <td class="text-end">
                                                <div class="d-flex align-items-center justify-content-end">
                                                    <span class="text-gray-900 fw-bold me-2">{{ $user->email }}</span>
                                                    <span class="badge {{ $user->email_verified_at ? 'badge-light-success' : 'badge-light-warning' }}">
                                                        {{ $user->email_verified_at ? 'Verified' : 'Unverified' }}
                                                    </span>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Phone</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $user->phone }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Username</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $user->username }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Role</td>
                                            <td class="text-end">
                                                <span class="badge badge-light-info">{{ ucfirst($user->getRoleNames()->first() ?? 'User') }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Member Since</td>
                                            <td class="text-gray-900 fw-bold text-end">{{ $user->created_at->format('M j, Y') }}</td>
                                        </tr>
                                    </tbody>
                                    <!--end::Table body-->
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table container-->
                        </div>
                        <!--begin::Body-->
                    </div>
                    <!--end::Contact Information Card-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->

            <!--begin::Row-->
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <!--begin::Col-->
                <div class="col-xl-8">
                    <!-- Transactions section removed -->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-xl-4">
                    <!-- Quick Actions section removed -->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Row-->

        @endif
    </div>
    <!--end::Container-->
@stop
