@extends('layouts.app')

@section('title', 'Create SMS Server')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Server Create-->
        <div class="row g-7">
            <!--begin::Content-->
            <div class="col-lg-6 col-xl-9">
                <!--begin::Contacts-->
                <div class="card card-flush h-lg-100" id="kt_contacts_main">
                    <!--begin::Card header-->
                    <div class="card-header pt-7" id="kt_chat_contacts_header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <!--begin::Svg Icon | path: icons/duotune/communication/com005.svg-->
                            <span class="svg-icon svg-icon-1 me-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 14H18V10H20C20.6 10 21 10.4 21 11V13C21 13.6 20.6 14 20 14ZM21 19V17C21 16.4 20.6 16 20 16H18V20H20C20.6 20 21 19.6 21 19ZM21 7V5C21 4.4 20.6 4 20 4H18V8H20C20.6 8 21 7.6 21 7Z" fill="currentColor" />
                                    <path opacity="0.3" d="M17 22H3C2.4 22 2 21.6 2 21V3C2 2.4 2.4 2 3 2H17C17.6 2 18 2.4 18 3V21C18 21.6 17.6 22 17 22ZM10 7C8.9 7 8 7.9 8 9C8 10.1 8.9 11 10 11C11.1 11 12 10.1 12 9C12 7.9 11.1 7 10 7ZM13.3 16C14 16 14.5 15.3 14.3 14.7C13.7 13.2 12 12 10.1 12C8.10001 12 6.49999 13.1 5.89999 14.7C5.59999 15.3 6.19999 16 7.39999 16H13.3Z" fill="currentColor" />
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                            <h2>Create SMS Server</h2>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-5">
                        <!--begin::Form-->
                        <form method="POST" class="form" action="{{ route('admin.servers.store') }}">
                            @csrf
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Gateway Provider Type</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the SMS gateway provider type."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Select2-->
                                <select class="form-select form-select-solid" name="gateway_type" id="gateway_type" required>
                                    <option value="">Select Gateway Provider</option>
                                    @foreach($gatewayProviderTypes as $providerType)
                                        <option value="{{ $providerType->name }}"
                                                data-auth-method="{{ $providerType->auth_method }}"
                                                data-http-method="{{ $providerType->http_method }}"
                                                data-description="{{ $providerType->description }}"
                                                {{ old('gateway_type') == $providerType->name ? 'selected' : '' }}>
                                            {{ $providerType->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <!--end::Select2-->
                                <div class="form-text" id="gateway_description"></div>
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Server Name</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter a unique name for this server configuration."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" class="form-control form-control-solid" name="name" value="{{ old('name') }}" placeholder="e.g., My RouteMobile Server" required />
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">API Endpoint URL</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the complete API endpoint URL."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="url" class="form-control form-control-solid" name="api_link" value="{{ old('api_link') }}" placeholder="https://api.provider.com/sms/send" required />
                                <!--end::Input-->
                            </div>
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Api Key</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the API key."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" class="form-control form-control-solid" name="api_key" value="{{ old('api_key') }}" />
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Row-->
                            <div class="row row-cols-1 row-cols-sm-2 rol-cols-md-1 row-cols-lg-2">
                                <!--begin::Col-->
                                <div class="col">
                                    <!--begin::Input group-->
                                    <div class="fv-row mb-7">
                                        <!--begin::Label-->
                                        <label class="fs-6 fw-semibold form-label mt-3">
                                            <span>Username</span>
                                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the username."></i>
                                        </label>
                                        <!--end::Label-->
                                        <!--begin::Input-->
                                        <input type="text" class="form-control form-control-solid" name="username" value="{{ old('username') }}"/>
                                        <!--end::Input-->
                                    </div>
                                    <!--end::Input group-->
                                </div>
                                <!--end::Col-->
                                <!--begin::Col-->
                                <div class="col">
                                    <!--begin::Input group-->
                                    <div class="fv-row mb-7">
                                        <!--begin::Label-->
                                        <label class="fs-6 fw-semibold form-label mt-3">
                                            <span class="required">Password</span>
                                        </label>
                                        <!--end::Label-->
                                        <div class="w-100">
                                            <!--begin::Input-->
                                            <input type="text" class="form-control form-control-solid" name="password" value="{{ old('password') }}"/>
                                            <!--end::Input-->
                                        </div>
                                    </div>
                                    <!--end::Input group-->
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Status</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Status"></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <div class="row row-cols-2 row-cols-md-2 row-cols-lg-2 row-cols-xl-2 g-3"
                                     data-kt-buttons="true" data-kt-buttons-target="[data-kt-button='true']">
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label class="btn btn-outline btn-outline-dashed btn-active-light-primary {{ old('status', 'enabled') == 'enabled' ? 'active' : '' }} d-flex text-start p-6" data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio" name="status" value="enabled" {{ old('status', 'enabled') == 'enabled' ? 'checked' : '' }}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Enabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label class="btn btn-outline btn-outline-dashed btn-active-light-primary {{ old('status', 'enabled') == 'disabled' ? 'active' : '' }} d-flex text-start p-6" data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio" name="status" value="disabled" {{ old('status', 'enabled') == 'disabled' ? 'checked' : '' }}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Disabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                </div>
                                <!--end::Row-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Separator-->
                            <div class="separator mb-6"></div>
                            <!--end::Separator-->
                            <!--begin::Action buttons-->
                            <div class="d-flex justify-content-end">
                                <!--begin::Button-->
                                <button type="reset" data-kt-contacts-type="cancel" class="btn btn-light me-3">Cancel</button>
                                <!--end::Button-->
                                <!--begin::Button-->
                                <button type="submit" data-kt-contacts-type="submit" class="btn btn-primary">
                                    <span class="indicator-label">Save</span>
                                    <span class="indicator-progress">Please wait...
                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                    </span>
                                </button>
                                <!--end::Button-->
                            </div>
                            <!--end::Action buttons-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Contacts-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Server Create-->
    </div>
    <!--end::Container-->
@stop

@push('scripts')
<script>
$(document).ready(function() {
    // Handle gateway type selection
    $('#gateway_type').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const authMethod = selectedOption.data('auth-method');
        const description = selectedOption.data('description');

        // Update description
        $('#gateway_description').text(description || '');

        // Show/hide authentication fields based on auth method
        toggleAuthFields(authMethod);

        // Load gateway configuration if not custom
        if ($(this).val() && $(this).val() !== 'custom') {
            loadGatewayConfig($(this).val());
        }
    });

    // Initialize form based on current selection
    if ($('#gateway_type').val()) {
        $('#gateway_type').trigger('change');
    }

    function toggleAuthFields(authMethod) {
        // Hide all auth fields first
        $('#api_key_field, #username_field, #password_field').hide();

        // Show relevant fields based on auth method
        switch(authMethod) {
            case 'api_key':
                $('#api_key_field').show();
                break;
            case 'username_password':
            case 'basic_auth':
                $('#username_field, #password_field').show();
                break;
            case 'bearer_token':
                $('#api_key_field').show();
                $('#api_key_field label span').text('Bearer Token');
                break;
            default:
                $('#api_key_field, #username_field, #password_field').show();
        }
    }

    function loadGatewayConfig(gatewayType) {
        $.get('{{ route("admin.servers.gateway-config") }}', {
            gateway_type: gatewayType
        })
        .done(function(data) {
            if (data.provider_type) {
                // You can populate additional fields here if needed
                console.log('Gateway config loaded:', data);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load gateway config:', xhr.responseJSON);
        });
    }
});
</script>
@endpush
