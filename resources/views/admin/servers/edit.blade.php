@extends('layouts.app')

@section('title', 'Edit SMS Server')

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Server Edit-->
        <div class="row g-7">
            <!--begin::Content-->
            <div class="col-lg-6 col-xl-9">
                <!--begin::Contacts-->
                <div class="card card-flush h-lg-100" id="kt_contacts_main">
                    <!--begin::Card header-->
                    <div class="card-header pt-7" id="kt_chat_contacts_header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <!--begin::Svg Icon | path: icons/duotune/communication/com005.svg-->
                            <span class="svg-icon svg-icon-1 me-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 14H18V10H20C20.6 10 21 10.4 21 11V13C21 13.6 20.6 14 20 14ZM21 19V17C21 16.4 20.6 16 20 16H18V20H20C20.6 20 21 19.6 21 19ZM21 7V5C21 4.4 20.6 4 20 4H18V8H20C20.6 8 21 7.6 21 7Z" fill="currentColor" />
                                    <path opacity="0.3" d="M17 22H3C2.4 22 2 21.6 2 21V3C2 2.4 2.4 2 3 2H17C17.6 2 18 2.4 18 3V21C18 21.6 17.6 22 17 22ZM10 7C8.9 7 8 7.9 8 9C8 10.1 8.9 11 10 11C11.1 11 12 10.1 12 9C12 7.9 11.1 7 10 7ZM13.3 16C14 16 14.5 15.3 14.3 14.7C13.7 13.2 12 12 10.1 12C8.10001 12 6.49999 13.1 5.89999 14.7C5.59999 15.3 6.19999 16 7.39999 16H13.3Z" fill="currentColor" />
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                            <h2>Edit SMS Server</h2>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-5">
                        <!--begin::Form-->
                        <form method="POST" class="form" action="{{ route('admin.servers.update', $server->id) }}">
                            @csrf
                            @method('PATCH')
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Gateway Provider Type</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the SMS gateway provider type."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Select2-->
                                <select class="form-select form-select-solid" name="gateway_type" id="gateway_type" required>
                                    <option value="">Select Gateway Provider</option>
                                    @foreach($gatewayProviderTypes as $providerType)
                                        <option value="{{ $providerType->name }}"
                                                data-auth-method="{{ $providerType->auth_method }}"
                                                data-http-method="{{ $providerType->http_method }}"
                                                data-description="{{ $providerType->description }}"
                                                {{ $server->gateway_type == $providerType->name ? 'selected' : '' }}>
                                            {{ $providerType->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <!--end::Select2-->
                                <div class="form-text" id="gateway_description"></div>
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Server Name</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter a unique name for this server configuration."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" class="form-control form-control-solid" name="name" value="{{ old('name', $server->name) }}" placeholder="e.g., My RouteMobile Server" required />
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Api Link</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the contact's phone."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="url" class="form-control form-control-solid" name="api_link" value="{{$server->api_link}}" required />
                                <!--end::Input-->
                            </div>
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Api Key</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the contact's phone."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" class="form-control form-control-solid" name="api_key" value="{{$server->api_key}}" />
                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Row-->
                            <div class="row row-cols-1 row-cols-sm-2 rol-cols-md-1 row-cols-lg-2">
                                <!--begin::Col-->
                                <div class="col">
                                    <!--begin::Input group-->
                                    <div class="fv-row mb-7">
                                        <!--begin::Label-->
                                        <label class="fs-6 fw-semibold form-label mt-3">
                                            <span>Username</span>
                                            <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the contact's Operator."></i>
                                        </label>
                                        <!--end::Label-->
                                        <!--begin::Input-->
                                        <input type="text" class="form-control form-control-solid" name="username" value="{{$server->username}}"/>
                                        <!--end::Input-->
                                    </div>
                                    <!--end::Input group-->
                                </div>
                                <!--end::Col-->
                                <!--begin::Col-->
                                <div class="col">
                                    <!--begin::Input group-->
                                    <div class="fv-row mb-7">
                                        <!--begin::Label-->
                                        <label class="fs-6 fw-semibold form-label mt-3">
                                            <span class="required">Password</span>
                                        </label>
                                        <!--end::Label-->
                                        <div class="w-100">
                                            <!--begin::Input-->
                                            <input type="text" class="form-control form-control-solid" name="password" value="{{$server->password}}"/>
                                            <!--end::Input-->
                                        </div>
                                    </div>
                                    <!--end::Input group-->
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Status</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Status"></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <!--begin::Row-->
                                <div class="row row-cols-2 row-cols-md-2 row-cols-lg-2 row-cols-xl-2 g-3"
                                     data-kt-buttons="true" data-kt-buttons-target="[data-kt-button='true']">
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label
                                            class="btn btn-outline btn-outline-dashed btn-active-light-primary {{$server->status=='enabled'?'active':''}} d-flex text-start p-6"
                                            data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span
                                                class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio"
                                                       name="status" value="enabled" {{$server->status=='enabled'?'checked="checked"':''}}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Enabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label
                                            class="btn btn-outline btn-outline-dashed btn-active-light-primary {{$server->status=='disabled'?'active':''}} d-flex text-start p-6"
                                            data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span
                                                class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio"
                                                       name="status" value="disabled" {{$server->status=='disabled'?'checked="checked"':''}}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Disabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                </div>
                                <!--end::Row-->

                                <!--end::Input-->
                            </div>
                            <!--end::Input group-->

                            <!--begin::API Parameters Section-->
                            <div class="fv-row mb-7" id="api_parameters_section" style="display: none;">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>API Parameters</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Configure the parameters that will be sent to the SMS provider's API"></i>
                                </label>
                                <!--end::Label-->

                                <!--begin::Parameters Info-->
                                <div class="alert alert-info d-flex align-items-center p-5 mb-5">
                                    <i class="ki-duotone ki-information-5 fs-2hx text-info me-4">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <div class="d-flex flex-column">
                                        <h4 class="mb-1 text-info">Available Placeholders</h4>
                                        <span>Use these placeholders in your parameter values: <code>{api_key}</code>, <code>{username}</code>, <code>{password}</code>, <code>{to}</code>, <code>{sender}</code>, <code>{message}</code>, <code>{type}</code>, <code>{timestamp}</code></span>
                                    </div>
                                </div>
                                <!--end::Parameters Info-->

                                <!--begin::Parameters Container-->
                                <div id="api_parameters_container">
                                    <div class="row mb-3">
                                        <div class="col-5">
                                            <label class="form-label fw-bold">Parameter Name</label>
                                        </div>
                                        <div class="col-5">
                                            <label class="form-label fw-bold">Parameter Value</label>
                                        </div>
                                        <div class="col-2">
                                            <label class="form-label fw-bold">Action</label>
                                        </div>
                                    </div>

                                    <!--begin::Parameter Row Template-->
                                    <div class="parameter-row row mb-3" style="display: none;" id="parameter_row_template">
                                        <div class="col-5">
                                            <input type="text" class="form-control form-control-solid parameter-key" placeholder="e.g., api_key, to, message" />
                                        </div>
                                        <div class="col-5">
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-solid parameter-value" placeholder="e.g., {api_key}, {to}, {message}" />
                                                <button class="btn btn-light-primary btn-sm" type="button" onclick="showPlaceholderDropdown(this)">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <button type="button" class="btn btn-light-danger btn-sm remove-parameter">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <!--end::Parameter Row Template-->
                                </div>
                                <!--end::Parameters Container-->

                                <!--begin::Add Parameter Button-->
                                <div class="d-flex justify-content-start mb-5">
                                    <button type="button" class="btn btn-light-primary btn-sm" id="add_parameter_btn">
                                        <i class="fas fa-plus"></i> Add Parameter
                                    </button>
                                </div>
                                <!--end::Add Parameter Button-->

                                <!--begin::Hidden Input for JSON-->
                                <input type="hidden" name="api_parameters" id="api_parameters_json" value="{{ json_encode($server->api_parameters ?? []) }}" />
                                <!--end::Hidden Input for JSON-->

                                <!--begin::Preview Section-->
                                <div class="card card-flush">
                                    <div class="card-header">
                                        <h3 class="card-title">JSON Preview</h3>
                                    </div>
                                    <div class="card-body">
                                        <pre id="json_preview" class="bg-light p-3 rounded">{{ json_encode($server->api_parameters ?? [], JSON_PRETTY_PRINT) }}</pre>
                                    </div>
                                </div>
                                <!--end::Preview Section-->
                            </div>
                            <!--end::API Parameters Section-->

                            <!--begin::Separator-->
                            <div class="separator mb-6"></div>
                            <!--end::Separator-->
                            <!--begin::Action buttons-->
                            <div class="d-flex justify-content-end">
                                <!--begin::Button-->
                                <button type="reset" data-kt-contacts-type="cancel" class="btn btn-light me-3">Cancel</button>
                                <!--end::Button-->
                                <!--begin::Button-->
                                <button type="submit" data-kt-contacts-type="submit" class="btn btn-primary">
                                    <span class="indicator-label">Save</span>
                                    <span class="indicator-progress">Please wait...
                                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                                <!--end::Button-->
                            </div>
                            <!--end::Action buttons-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Contacts-->
            </div>

            <!--end::Content-->
        </div>
        <!--end::Server Edit-->
    </div>
    <!--end::Container-->
@stop

@push('scripts')
<script>
$(document).ready(function() {
    // Handle gateway type selection
    $('#gateway_type').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const authMethod = selectedOption.data('auth-method');
        const description = selectedOption.data('description');

        // Update description
        $('#gateway_description').text(description || '');

        // Show/hide authentication fields based on auth method
        toggleAuthFields(authMethod);

        // Load gateway configuration if not custom
        if ($(this).val() && $(this).val() !== 'custom') {
            loadGatewayConfig($(this).val());
        }
    });

    // Initialize form based on current selection
    if ($('#gateway_type').val()) {
        $('#gateway_type').trigger('change');
    }

    // Initialize API Parameters functionality
    initializeApiParameters();

    function toggleAuthFields(authMethod) {
        // Hide all auth fields first
        $('#api_key_field, #username_field, #password_field').hide();

        // Show relevant fields based on auth method
        switch(authMethod) {
            case 'api_key':
                $('#api_key_field').show();
                break;
            case 'username_password':
            case 'basic_auth':
                $('#username_field, #password_field').show();
                break;
            case 'bearer_token':
                $('#api_key_field').show();
                $('#api_key_field label span').text('Bearer Token');
                break;
            default:
                $('#api_key_field, #username_field, #password_field').show();
        }
    }

    function loadGatewayConfig(gatewayType) {
        $.get('{{ route("admin.servers.gateway-config") }}', {
            gateway_type: gatewayType
        })
        .done(function(data) {
            if (data.provider_type) {
                // You can populate additional fields here if needed
                console.log('Gateway config loaded:', data);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load gateway config:', xhr.responseJSON);
        });
    }

    // API Parameters functionality
    function initializeApiParameters() {
        // Show/hide API parameters section based on gateway type
        $('#gateway_type').on('change', function() {
            const selectedValue = $(this).val();
            if (selectedValue === 'custom' || selectedValue === '') {
                $('#api_parameters_section').show();
                loadExistingParameters();
            } else {
                $('#api_parameters_section').hide();
            }
        });

        // Add parameter button
        $('#add_parameter_btn').on('click', function() {
            addParameterRow();
        });

        // Handle parameter changes
        $(document).on('input', '.parameter-key, .parameter-value', function() {
            updateJsonPreview();
        });

        // Handle remove parameter
        $(document).on('click', '.remove-parameter', function() {
            $(this).closest('.parameter-row').remove();
            updateJsonPreview();
        });

        // Initialize with existing parameters if custom is selected
        if ($('#gateway_type').val() === 'custom') {
            $('#api_parameters_section').show();
            loadExistingParameters();
        }
    }

    function addParameterRow(key = '', value = '') {
        const template = $('#parameter_row_template').clone();
        template.attr('id', '').show();
        template.find('.parameter-key').val(key);
        template.find('.parameter-value').val(value);

        $('#api_parameters_container').append(template);
        updateJsonPreview();
    }

    function loadExistingParameters() {
        // Clear existing parameters
        $('.parameter-row:not(#parameter_row_template)').remove();

        // Load existing parameters from server data
        const existingParams = @json($server->api_parameters ?? []);

        if (Object.keys(existingParams).length > 0) {
            Object.entries(existingParams).forEach(([key, value]) => {
                addParameterRow(key, value);
            });
        } else {
            // Add default parameters if none exist
            const defaultParams = [
                { key: 'api_key', value: '{api_key}' },
                { key: 'to', value: '{to}' },
                { key: 'message', value: '{message}' },
                { key: 'from', value: '{sender}' }
            ];

            defaultParams.forEach(param => {
                addParameterRow(param.key, param.value);
            });
        }
    }

    function updateJsonPreview() {
        const parameters = {};
        $('.parameter-row:not(#parameter_row_template)').each(function() {
            const key = $(this).find('.parameter-key').val().trim();
            const value = $(this).find('.parameter-value').val().trim();

            if (key && value) {
                parameters[key] = value;
            }
        });

        const jsonString = JSON.stringify(parameters, null, 2);
        $('#json_preview').text(jsonString);
        $('#api_parameters_json').val(JSON.stringify(parameters));
    }

    function showPlaceholderDropdown(button) {
        const placeholders = [
            '{api_key}', '{username}', '{password}', '{bearer_token}',
            '{to}', '{sender}', '{message}', '{type}', '{type_mapped}',
            '{timestamp}', '{batch_id}', '{company_id}'
        ];

        // Create dropdown menu
        let dropdown = '<div class="dropdown-menu show" style="position: absolute; z-index: 1000;">';
        placeholders.forEach(placeholder => {
            dropdown += `<a class="dropdown-item" href="#" onclick="insertPlaceholder('${placeholder}', this)">${placeholder}</a>`;
        });
        dropdown += '</div>';

        // Remove existing dropdown
        $('.dropdown-menu').remove();

        // Add dropdown
        $(button).parent().append(dropdown);

        // Position dropdown
        const $dropdown = $(button).parent().find('.dropdown-menu');
        $dropdown.css({
            'top': $(button).outerHeight() + 'px',
            'left': '0px'
        });

        // Close dropdown when clicking outside
        $(document).on('click.placeholder-dropdown', function(e) {
            if (!$(e.target).closest('.input-group').length) {
                $('.dropdown-menu').remove();
                $(document).off('click.placeholder-dropdown');
            }
        });
    }

    function insertPlaceholder(placeholder, element) {
        const $inputGroup = $(element).closest('.input-group');
        const $input = $inputGroup.find('.parameter-value');
        const currentValue = $input.val();

        // Insert placeholder at cursor position or append
        $input.val(currentValue + placeholder);
        $input.focus();

        // Remove dropdown
        $('.dropdown-menu').remove();
        $(document).off('click.placeholder-dropdown');

        // Update preview
        updateJsonPreview();
    }
});
</script>
@endpush
